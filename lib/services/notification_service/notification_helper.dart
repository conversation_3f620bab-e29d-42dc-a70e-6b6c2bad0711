// ignore_for_file: avoid_bool_literals_in_conditional_expressions

import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:go_router/go_router.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/enum/chat_type.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/models/chat_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/add_checklist_page/models/add_checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/provider/checklist_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/send_offer_page/pages/offer_price_page/models/offer_price_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_string.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';

/// This is a Top-level function where it is used for handling background or
/// Terminated state notifications. This is optional if you don't use onBackgroundMessage stream
// @pragma('vm:entry-point')
// Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   'Handling a background message: ${message.messageId}'.logD;
// }

/// This class is helper for initialize Notification with get permission of user,
/// To handle foreground/background/terminated state notification.
class NotificationHelper {
  /*
  /// initialize and setup the notification for device.
  static Future<void> initializeNotification() async {
    // Getting the instance of firebase messaging
    final messaging = FirebaseMessaging.instance;
    await messaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    // initialize local notification for foreground notification.
    await LocalNotificationHelper.localNotificationHelper.initialize();

    /// Request the notification permission to user,
    /// in Android 12 or below,by default Notification permission is granted.
    if (Platform.isIOS) {
      await messaging.requestPermission();
    }

    /// To send notification to specific device, we need specific device token.
    /// To get device token, we can use following method
    (await messaging.getToken()).logD;

    // we can also send notification using subscribe the topic.
    // await FirebaseMessaging.instance.subscribeToTopic("myTopic");

    /// To handle Foreground notification
    FirebaseMessaging.onMessage.listen((event) {
      if (event.notification != null) {
        if (Platform.isAndroid) {
          LocalNotificationHelper.localNotificationHelper
              .showNotification(event);
        }
        //   final data = jsonDecode(
        //     (event.data)['data'].toString(),
        //   );
        //   final type = data['type'];
        //   if (data.toString().contains('type')) {}
      }
    });

    /// To handle Background/terminated app notification (This is optional.)
    // FirebaseMessaging.onBackgroundMessage(
    //   _firebaseMessagingBackgroundHandler,
    // );

    // To handle the Notification Tap Event on Background.
    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      '=====>>> bg: $event'.logD;
      notificationOnTapHandler(remoteMessage: event);
      // final data = jsonDecode(
      //   event.data['data'].toString(),
      // );
    });

    // To handle the Notification Tap Event on Terminated state.
    await FirebaseMessaging.instance.getInitialMessage().then((event) {
      '=====>>> tg:  $event'.logD;
      notificationOnTapHandler(remoteMessage: event);
    });
    // } else {
    //   // here you can give a message to the user if user not granted the permission.
    //   log('User declined the permission');
    // }
  }
*/
  /// Notification tap handler
  static Future<void> notificationOnTapHandler(
    RemoteMessage notificationData,
  ) async {
    final data = notificationData.data;
    final type = data[AppStrings.type];
    '==>>> type: $type'.logE;
    switch (type) {
      case AppStrings.chat:
        {
          final sender = jsonDecode((data[AppStrings.sender] as String?) ?? '');
          final chatRoom =
              jsonDecode((data[AppStrings.chatRoom] as String?) ?? '');
          final participants = chatRoom[AppStrings.participants];

          /// context
          final context = rootNavKey.currentContext!;

          /// get current route or screen
          final currentRoute = GoRouter.of(
            context,
          ).routerDelegate.currentConfiguration.last.matchedLocation;

          /// pop screen when we already in chat screen to avoid duplicate screen
          /// and avoid socket connection issue
          final isChatScreen = currentRoute == AppRoutes.tripsChatPath;
          if (isChatScreen) AppNavigationService.pop(context);

          if (sender is Map<String, dynamic> &&
              chatRoom is Map<String, dynamic> &&
              participants is List) {
            await AppNavigationService.pushNamed(
              context,
              AppRoutes.tripsChatScreen,
              extra: ChatParams(
                receiverId: participants
                        .where(
                          (e) => e[AppStrings.id] != sender[AppStrings.id],
                        )
                        .firstOrNull?[AppStrings.user] as int? ??
                    0,
                bookingDetailId:
                    (chatRoom[AppStrings.bookingDetail] as int?) ?? 0,
                chatType: ChatType.customer,
                customerChatRoomParameter: CustomerChatRoom.fromJson(chatRoom),
                title: (sender[AppStrings.firstName] as String?) ?? '',
              ),
              // ChangeNotifierProvider(
              //   create: (context) => ChatProvider(
              //     receiverId: participants
              //             .where(
              //               (e) => e[AppStrings.id] != sender[AppStrings.id],
              //             )
              //             .firstOrNull?[AppStrings.user] as int? ??
              //         0,
              //     bookingDetailId:
              //         (chatRoom[AppStrings.bookingDetail] as int?) ?? 0,
              //     chatType: ChatType.customer,
              //     customerChatRoomParameter:
              //         CustomerChatRoom.fromJson(chatRoom),
              //   ),
              //   child: ChatScreen(
              //     title: (sender[AppStrings.firstName] as String?) ?? '',
              //     customerChatRoomParameter:
              //         CustomerChatRoom.fromJson(chatRoom),
              //   ),
              // ),
            );
          }
        }
      case AppStrings.checklist:
        await AppNavigationService.pushNamed(
          rootNavKey.currentContext!,
          AppRoutes.tripsAddChecklistScreen,
          extra: AddChecklistParams(
            checkListProvider: CheckListProvider(
              carId: (data[AppStrings.bookedCarId] as String?) ?? '',
            ),
            checkListId: int.tryParse(
              (data[AppStrings.relatedObjectId] as String?) ?? '',
            ),
            isFromNotification: true,
            isVerify: data.containsKey(AppStrings.isVerified)
                ? data[AppStrings.isVerified] == false
                : false,
          ),
          // ChangeNotifierProvider(
          //   create: (context) => CheckListProvider(
          //     carId: (data[AppStrings.bookedCarId] as String?) ?? '',
          //   ),
          //   builder: (context, child) {
          //     return AddChecklistScreen(
          //       checkListProvider: Provider.of<CheckListProvider>(context),
          //       checkListId: int.tryParse(
          //         (data[AppStrings.relatedObjectId] as String?) ?? '',
          //       ),
          //     );
          //   },
          // ),
        );
      // final chatRoom = ChatRoom.fromJson(data);
      // Navigator.pushNamed(
      //   AppNavigator.navigatorKey.currentContext!,
      //   AppRoutes.chatScreen,
      //   arguments: chatRoom,
      // );
      case AppStrings.bookingDetailStr ||
            AppStrings.tripStr ||
            AppStrings.bookingStr:
        final booking = data[AppStrings.relatedObjectData] != null
            ? jsonDecode((data[AppStrings.relatedObjectData] as String?) ?? '')
            : null;
        if (booking is Map<String, dynamic> &&
            data[AppStrings.notToRedirect] == null) {
          '==>>> notification: $data \n ${data[AppStrings.bookingType]}'.logE;

          /// if notification is for exclusive trip, then show exclusive trip screen
          if (booking[AppStrings.bookingStatus] ==
              NotificationType.EXCLUSIVE.name) {
            if (booking[AppStrings.bookingType] == AppStrings.sentOfferStr) {
              await AppNavigationService.pushNamed(
                rootNavKey.currentContext!,
                AppRoutes.tripsOfferPriceScreen,
                extra: OfferPriceParams(
                  tripId: int.tryParse(
                    (data[AppStrings.relatedObjectId] as String?) ?? '',
                  ),
                ),
                // OfferPriceScreen(
                //   tripId: int.tryParse(
                //     (data[AppStrings.relatedObjectId] as String?) ?? '',
                //   ),
                // ),
              );
            } else {
              await AppNavigationService.pushNamed(
                rootNavKey.currentContext!,
                AppRoutes.tripsExclusiveRequestedScreen,
                extra: ExclusiveRequestedParams(
                  exclusiveBooking: ExclusiveTrip.fromJson(booking),
                  requestedTripProvider: RequestedTripProvider(),
                  tripDataProvider: TripDataProvider(),
                ),
                // MultiProvider(
                //   providers: [
                //     ChangeNotifierProvider(
                //       create: (context) => RequestedTripProvider(),
                //     ),
                //     ChangeNotifierProvider(
                //       create: (context) => TripDataProvider(),
                //     ),
                //   ],
                //   builder: (context, child) => ExclusiveRequestedScreen(
                //     exclusiveBooking: null,
                //     tripId: booking[AppStrings.bookingId] as int?,
                //     requestedTripProvider:
                //         Provider.of<RequestedTripProvider>(context),
                //     tripDataProvider: Provider.of<TripDataProvider>(context),
                //   ),
                //   // child: ExclusiveRequestedScreen(
                //   //   exclusiveBooking: null,
                //   //   tripId: booking[AppStrings.bookingId] as int?,
                //   //   requestedTripProvider: widget.requestedTripProvider,
                //   //   tripDataProvider: widget.tripDataProvider,
                //   // ),
                // ),
              );
            }

            /// if notification is for sent offer, then show offer price screen
          } else {
            await AppNavigationService.pushNamed(
              rootNavKey.currentContext!,
              AppRoutes.tripsAllShipmentsScreen,
              extra: AllShipmentsParams(
                tripId: booking[AppStrings.tripId] as int?,
                bookingId: booking[AppStrings.bookingId] as int?,
              ),
              // AllShipmentsScreen(
              //   tripId: booking[AppStrings.tripId] as int?,
              //   bookingId: booking[AppStrings.bookingId] as int?,
              // ),
            );
          }
        } else {
          await AppNavigationService.pushNamed(
            rootNavKey.currentContext!,
            AppRoutes.tripsAllShipmentsScreen,
            extra: AllShipmentsParams(
              tripId: int.tryParse(
                (data[AppStrings.relatedObjectId] as String?) ?? '',
              ),
            ),
            // {
            //   'tripId': int.tryParse(
            //     (data[AppStrings.relatedObjectId] as String?) ?? '',
            //   ),
            //   // 'reportId': int.tryParse(
            //   //   (data[AppStrings.reportId] as String?) ?? '',
            //   // ),
            // },
            // AllShipmentsScreen(
            //   tripId: int.tryParse(
            //     (data[AppStrings.relatedObjectId] as String?) ?? '',
            //   ),
            //   // reportId: int.tryParse(
            //   //   (data[AppStrings.reportId] as String?) ?? '',
            //   // ),
            // ),
          );
        }
      // related_object_data
    }
  }
}
