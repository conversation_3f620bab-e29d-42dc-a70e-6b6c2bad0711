// ignore_for_file: constant_identifier_names, public_member_api_docs

/// API endpoints
final class EndPoints {
  // /// Api base url
  // static const String baseUrl = 'http://192.168.0.72:8001/api/';

  // ^ Header value
  static const Header_Content_Key = 'Content-Type';
  static const Header_Content_value = 'application/json';
  static const Header_Content_valueFormData = 'application/form-data';
  static const Header_Content_value_Url_Encoded =
      'application/x-www-form-urlencoded';
  static const Header_Authorization_KEY = 'Authorization';
  static const Header_Accept_Key = 'Accept';
  static const BEARER_TOKEN = 'Bearer ';

  /// Endpoint for fetching users
  static const String signup = 'auth/public/sign-up/';
  static const String signin = 'auth/public/sign-in/';
  static const String verifyOtp = 'auth/public/verify-otp/';
  static const String resendOtp = 'auth/public/resend-otp/';
  static const String activateStripeAccount = 'auth/activate-stripe-account/';
  static const String stripeConnectAccountLoginUrl =
      'auth/stripe-connect-account-login-url/';
  static const String sendForgotPasswordOtp =
      'auth/public/send-forgot-password-otp/';
  static const String resetPassword = 'auth/public/reset-password/';
  static const String refreshToken = 'token/refresh/';
  static const String getUserInfo = 'auth/user-profile-detail/';
  static const String providerInfo = 'auth/provider-detail/';
  static const String logout = 'auth/logout/';
  static const String deleteAccount = 'auth/account/';

  /// Endpoint for Equipments
  static const String getEquipments = 'provider/equipments/';
  static const String addEquipment = 'provider/equipment/create/';
  static const String getEquipmentsForCreation =
      'provider/list-equipment-for-creation/';
  static const String createEquipment = 'provider/equipment/create/';
  static const String getEquipmentsBrands = 'provider/equipment-brands/all/';
  static const String deleteEquipment = 'provider/equipment/';
  static const String updateEquipment = 'provider/equipment/';

  /// Endpoint for Drivers
  static const String getDrivers = 'provider/drivers/';
  static const String createDriver = 'provider/driver/create/';
  static const String deleteDriver = 'provider/driver/';

  /// Endpoint for create trip
  static const String listNearByLocation =
      'provider/list-near-by-stop-locations';
  static const String listStopLocation =
      'provider/list-stop-location-to-find-intermediate-pickup-point/all/';
  static const String createRoute = 'provider/shared-trip/create/';
  static const String getTripList = 'provider/trips/';
  static const String getSavedTripList = 'provider/list-saved-route/';
  static const String getSentOfferTrips = 'provider/sent-offer-trips/';
  static const String getSentOfferDetail = 'provider/sent-offer-trip/';
  static String latestChecklist(int id) =>
      'provider/booking/booked-car/$id/latest-checklist/';
  static String checklistDetail(int id) =>
      'provider/booking/booked-car/checklist/$id/';
  static const String rejectExclusiveTripOffer =
      'provider/reject-exclusive-booking-request/';
  static const String getCurrentOffers = 'provider/list-current-offers/';
  static const String createReport = 'provider/trip/report/create/';
  static const String createCheckList =
      'provider/booking/booked-car/create/checklist/';
  static String getRequestedTripDetail(String id) =>
      'provider/requested-exclusive-bookings/$id/';
  static String getAcceptedTrips(String status) =>
      'provider/accepted-trips/?status=$status';
  static String getAcceptedTripDetail(String id) =>
      'provider/accepted-trip/$id/';
  static String getAcceptedTripBookings(String id) =>
      'provider/list-trip-bookings/?trip_id=$id';
  static const String createExclusiveTripOffer =
      'provider/create-exclusive-trip-offer/';
  static String updateExclusiveTripOffer(String offerId) =>
      'provider/exclusive-trip/$offerId/update/';
  static const String getRequestedExclusiveBookings =
      'provider/list-requested-exclusive-bookings/';
  static String updateTrip(String id) => 'provider/shared-trip/$id/update/';
  static String deleteTrip(String id) => 'provider/trip/$id/cancel/';
  static String startTrip(String id) => 'provider/trip/$id/start/';
  static String completeTrip(String id) => 'provider/trip/$id/completed/';
  static String outForDelivery(String id) =>
      'provider/trip/$id/out-for-delivery/';
  static String readyToCollect(String id) =>
      'provider/exclusive-trip/$id/all-unit-ready-to-collect/';
  static String getCheckList(String carId) =>
      'provider/booking/booked-car/$carId/checklists/';
  static String deleteSaveRoute(String id) =>
      'provider/unsave-route/$id/delete/';
  static String updateCost(String id) =>
      'provider/sent-offer-trip/$id/update/cost/';
  static String getTripDetail(String id) => 'provider/trip/$id/';
  static const String generateUrl = 'auth/generate-presigned-url/';
  static const String notifications = 'provider/notifications/';
  static const String unreadNotificationCount =
      'provider/notifications/unread/';
  static const String notificationMarkReaded =
      'provider/notifications/mark-readed/';

  /// Customer Support
  static const String customerSupport = 'provider/complaint/create/';

  /// Chat
  static String getOldChatMessages(String id) => 'provider/chat/$id/messages/';
  static String markAllChatMessagesReaded(String id) =>
      '/provider/chat/$id/messages/mark-as-readed/';

  /// Dropdown value
  static const String driverDropDownData = 'provider/drop-down/drivers/all/';
  static const String equipmentDropDownData =
      'provider/drop-down/equipments/all/';

  /// Driver checklist endpoints
  static const String getPendingVerificationChecklists =
      'provider/checklists/to-verify/';
  static const String createChecklistComment =
      'provider/checklist-comments/create/';
}
