import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/driver_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/equipment_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/sent_offer_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/checklist_api_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/pages/chat_page/models/chat_messages_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';
import 'package:transportmatch_provider/presentation/modules/driver_module/drive_checklist_page/models/pending_verification_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/models/checklist_comment_model.dart';
import 'package:transportmatch_provider/shared/rest_api/rest_api.dart';

/// Equipments related API methods class
final class TripRepository {
  /// User account repository constructor
  const TripRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// for get stock location by location
  Future<ApiResult<StockLocationModel>> listNearByLocation(
    ApiRequest request,
  ) {
    return DioRequest<StockLocationModel>(
      dio: dio,
      path: request.path!,
      listJsonMapper: StockLocationModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get stock location by location
  Future<ApiResult<StopLocationModel>> listStopLocation(
    ApiRequest request,
  ) {
    return DioRequest<StopLocationModel>(
      dio: dio,
      path: request.path!,
      listJsonMapper: StopLocationModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get stock location by location
  Future<ApiResult<dynamic>> createRoute(
    ApiRequest request,
  ) {
    return DioRequest<dynamic>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// for get stock location by location
  Future<ApiResult<dynamic>> updateRoute(
    ApiRequest request,
  ) {
    return DioRequest<dynamic>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  /// for get stock location by location
  Future<ApiResult<dynamic>> cancelTrip(
    ApiRequest request,
  ) {
    return DioRequest<dynamic>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).put();
  }

  /// for get stock location by location
  Future<ApiResult<ExclusiveTripModel>> getRequestedExclusiveBookings(
    ApiRequest request,
  ) {
    return DioRequest<ExclusiveTripModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ExclusiveTripModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get stock location by location
  Future<ApiResult<SentOfferModel>> getSentExclusiveBookings(
    ApiRequest request,
  ) {
    return DioRequest<SentOfferModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: SentOfferModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get sent offer trip detail
  Future<ApiResult<SentOffer>> getSentBookingDetail(
    ApiRequest request,
  ) {
    return DioRequest<SentOffer>(
      dio: dio,
      path: request.path!,
      jsonMapper: SentOffer.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get Current Offers
  Future<ApiResult<List<OfferProviderModel>>> getCurrentOffers(
    ApiRequest request,
  ) {
    return DioRequest<List<OfferProviderModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (p0) => p0
          .map((x) => OfferProviderModel.fromJson(x as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get accepted trip detail
  Future<ApiResult<TripDetailsModel>> getAcceptedTripDetail(
    ApiRequest request,
  ) {
    return DioRequest<TripDetailsModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: TripDetailsModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get accepted trip booking details
  Future<ApiResult<List<BookingModel>>> getAcceptedTripBookingDetail(
    ApiRequest request,
  ) {
    return DioRequest<List<BookingModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (p0) => p0
          .map((x) => BookingModel.fromJson(x as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get stock location by location
  Future<ApiResult<Map<String, dynamic>>> createExclusiveTripOffer(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// for updating exclusive trip offer
  Future<ApiResult<Map<String, dynamic>>> updateExclusiveTripOffer(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  /// for get stock location by location
  Future<ApiResult<ExclusiveTrip>> getRequestedTripDetail(
    ApiRequest request,
  ) {
    return DioRequest<ExclusiveTrip>(
      dio: dio,
      path: request.path!,
      jsonMapper: ExclusiveTrip.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get stock location by location
  Future<ApiResult<AcceptedTripFullModel>> getAcceptedTrips(
    ApiRequest request,
  ) {
    return DioRequest<AcceptedTripFullModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: AcceptedTripFullModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for get stock location by location
  Future<ApiResult<Map<String, dynamic>>> rejectExclusiveTrip(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// for start trip
  Future<ApiResult<Map<String, dynamic>>> startOngoingOutDeliveryTrip(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  /// for start trip
  Future<ApiResult<Map<String, dynamic>>> updateCost(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  /// for start trip
  Future<ApiResult<ChecklistDataModel>> getCheckList(
    ApiRequest request,
  ) {
    return DioRequest<ChecklistDataModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ChecklistDataModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for start trip
  Future<ApiResult<ChecklistApiModel>> getChecklistData(
    ApiRequest request,
  ) {
    return DioRequest<ChecklistApiModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ChecklistApiModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// for start trip
  Future<ApiResult<Map<String, dynamic>>> createCheckList(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// for report trip
  Future<ApiResult<Map<String, dynamic>>> reportTrip(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// to get all old messages
  Future<ApiResult<ChatMessagesModel>> getOldChatMessages(
    ApiRequest request,
  ) {
    return DioRequest<ChatMessagesModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ChatMessagesModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// to read all chat messages
  Future<ApiResult<Map<String, dynamic>>> markAllChatMessagesAsReaded(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (p0) => p0,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).patch();
  }

  /// Get Driver DropDown Data
  Future<ApiResult<List<DriverDropDownModel>>> getDriverDropDownData(
    ApiRequest request,
  ) {
    return DioRequest<List<DriverDropDownModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (p0) => p0
          .map((x) => DriverDropDownModel.fromJson(x as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get Equipment DropDown Data
  Future<ApiResult<List<EquipmentDropDownModel>>> getEquipmentDropDownData(
    ApiRequest request,
  ) {
    return DioRequest<List<EquipmentDropDownModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (p0) => p0
          .map(
            (x) => EquipmentDropDownModel.fromJson(x as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Api call to get all live trip
  Future<ApiResult<StockLocationData>> getTripList(
    ApiRequest request,
  ) {
    return DioRequest<StockLocationData>(
      dio: dio,
      path: request.path!,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      jsonMapper: StockLocationData.fromJson,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Api call to get all saved trip
  Future<ApiResult<StockLocationData>> getSavedTripList(
    ApiRequest request,
  ) {
    return DioRequest<StockLocationData>(
      dio: dio,
      path: request.path!,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      jsonMapper: StockLocationData.fromJson,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Api call to get trip detail
  Future<ApiResult<SavedRoute>> getTripDetail(
    ApiRequest request,
  ) {
    return DioRequest<SavedRoute>(
      dio: dio,
      path: request.path!,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      jsonMapper: SavedRoute.fromJson,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Api call to delete saved route
  Future<ApiResult<Map<String, dynamic>>> deleteSaveRoute(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      jsonMapper: (p0) => p0,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).delete();
  }

  /// Api call to get pending verification checklists
  Future<ApiResult<PendingVerificationModel>> getPendingVerificationChecklists(
    ApiRequest request,
  ) {
    return DioRequest<PendingVerificationModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: PendingVerificationModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Api call to create checklist comment
  Future<ApiResult<ChecklistCommentResponseModel>> createChecklistComment(
    ApiRequest request,
  ) {
    return DioRequest<ChecklistCommentResponseModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ChecklistCommentResponseModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }
}
