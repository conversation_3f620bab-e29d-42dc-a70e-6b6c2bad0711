import 'package:flutter/material.dart';
import 'package:transportmatch_provider/utils/app_size.dart';

/// This is loading indicator widget
class LoadingIndicator extends StatelessWidget {
  /// Constructor
  const LoadingIndicator({
    super.key,
    this.progress,
    this.color,
    this.showLinearLoader = false,
    this.strokeWidth = 4.0,
  });

  /// Loading progress between 0 - 1
  final double? progress;

  /// Color of loading indicator
  final Color? color;

  /// The width of the line used to draw the circle.
  final double strokeWidth;

  /// Flag for showing linear progress bar
  final bool showLinearLoader;

  @override
  Widget build(BuildContext context) {
    return showLinearLoader
        ? Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSize.w10),
            child: LinearProgressIndicator(
              value: progress,
            ),
          )
        : CircularProgressIndicator(
            strokeWidth: strokeWidth,
            valueColor: AlwaysStoppedAnimation<Color>(color ?? Colors.white),
            value: progress,
          );
  }
}
