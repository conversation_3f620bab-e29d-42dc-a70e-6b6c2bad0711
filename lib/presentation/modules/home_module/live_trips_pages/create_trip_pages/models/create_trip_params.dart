// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/models/save_trip_model.dart';

/// Parameters for the NewRouteScreen
class CreateTripParams {
  /// Constructor
  CreateTripParams({
    this.savedRouteData,
    this.isSetSavedRoute = false,
    this.tripId,
  });

  /// Whether this is setting a saved route
  final bool isSetSavedRoute;

  /// The saved route data
  final SavedRoute? savedRouteData;

  final String? tripId;
}
