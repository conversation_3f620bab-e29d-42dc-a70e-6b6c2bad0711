// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/provider/create_trip_provider.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';

class TripCostWidget extends StatelessWidget {
  const TripCostWidget({
    required this.createTripProvider,
    super.key,
    required this.isReadOnly,
  });

  final CreateTripProvider createTripProvider;
  final bool isReadOnly;

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: isReadOnly ? 0.5 : 1,
      child: AbsorbPointer(
        absorbing: isReadOnly,
        child: Column(
          children: [
            AppTextFormField(
              controller: createTripProvider.kmPerController,
              hintText: context.l10n.enterPerKm,
              textAction:TextInputAction.next,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              readOnly: isReadOnly,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}$')),
              ],
              onTap: () {
                if (createTripProvider.distance.value <= 0) {
                  context.l10n.tripDistanceIsZeroPleaseSelectOriginDropLocation
                      .showErrorAlert();
                }
              },
              onChanged: (p0) {
                if (p0 != null &&
                    p0.isNotEmpty &&
                    createTripProvider.distance.value > 0) {
                  final costPerKm = double.tryParse(p0);
                  if (costPerKm != null) {
                    final distanceInKm = createTripProvider.distance.value / 1000;
                    // Calculate total cost based on cost per km and distance
                    final totalCost = costPerKm * distanceInKm;
                    createTripProvider.totalTripController.text =
                        totalCost.toStringAsFixed(2); // Keep 2 decimal places
                  }
                } else {
                  createTripProvider.totalTripController.clear();
                }
              },
            ),
            Gap(AppSize.h12),
            AppTextFormField(
              controller: createTripProvider.totalTripController,
              hintText: context.l10n.totalCostOfTrip,
              textAction:TextInputAction.next,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              readOnly: isReadOnly,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}$')),
              ],
              onTap: () {
                if (createTripProvider.distance.value <= 0) {
                  context.l10n.tripDistanceIsZeroPleaseSelectOriginDropLocation
                      .showErrorAlert();
                }
              },
              onChanged: (p0) {
                if (p0 != null &&
                    p0.isNotEmpty &&
                    createTripProvider.distance.value > 0) {
                  final totalCost = double.tryParse(p0);
                  if (totalCost != null) {
                    final distanceInKm = createTripProvider.distance.value / 1000;
                    if (distanceInKm > 0) {
                      // Update cost per km field
                      final costPerKm = totalCost / distanceInKm;
                      createTripProvider.kmPerController.text =
                          costPerKm.toStringAsFixed(2); // Keep 2 decimal places
                    }
                  }
                } else {
                  createTripProvider.kmPerController.clear();
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
