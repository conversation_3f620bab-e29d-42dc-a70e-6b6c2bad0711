import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/auth_module/pages/signup_page/provider/signup_provider.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';

import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/utils/validators/password_match_validator.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

/// Signup Screen
class SignupPage extends StatelessWidget {
  /// Signup Screens
  const SignupPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<SignupProvider>(
      create: (context) => SignupProvider(),
      child: Consumer<SignupProvider>(
        builder: (context, signupProvider, _) {
          return Scaffold(
            appBar: const CustomAppBar(
              title: '',
              backgroundColor: AppColors.white,
            ),
            body: GestureDetector(
              onTap: AppCommonFunctions.closeKeyboard,
              child: ValueListenableBuilder(
                valueListenable: signupProvider.isShowLoader,
                builder: (context, isLoading, child) {
                  return AppLoader(isShowLoader: isLoading, child: child!);
                },
                child: AppPadding.symmetric(
                  horizontal: AppSize.appPadding,
                  child: SizedBox(
                    height: context.height,
                    child: Form(
                      key: signupProvider.formKeySignup,
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Gap(AppSize.h30),
                            Text(
                              context.l10n.signUp,
                              style: context.textTheme.headlineLarge?.copyWith(
                                fontSize: AppSize.sp24,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            Gap(AppSize.h8),
                            Text(
                              context.l10n.pleaseEnterDetails,
                              style: context.textTheme.bodySmall?.copyWith(
                                fontSize: AppSize.sp16,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            Gap(AppSize.h40),
                            //* Name
                            AppTextFormField(
                              title: context.l10n.name,
                              hintText: context.l10n.enterYourName,
                              textAction:TextInputAction.next,
                              fillColor: AppColors.ffF8F9FA,
                              controller: signupProvider.nameController,
                              validator: (p0) => nameValidator().call(p0),
                            ),
                            Gap(AppSize.h28),
                            //* Email
                            AppTextFormField(
                              title: context.l10n.email,
                              hintText: context.l10n.enterYourEmail,
                              textAction:TextInputAction.next,
                              keyboardType: TextInputType.emailAddress,
                              fillColor: AppColors.ffF8F9FA,
                              controller: signupProvider.emailController,
                              validator: (p0) => emailValidator().call(p0),
                            ),
                            Gap(AppSize.h28),
                            //* Password
                            ValueListenableBuilder(
                              valueListenable: signupProvider.isPasswordShow,
                              builder: (context, isPasswordShow, _) {
                                return AppTextFormField(
                                  title: context.l10n.password,
                                  hintText: context.l10n.enterYourPassword,
                                  textAction:TextInputAction.next,
                                  fillColor: AppColors.ffF8F9FA,
                                  obscureText:
                                      !signupProvider.isPasswordShow.value,
                                  controller: signupProvider.passwordController,
                                  validator: (p0) =>
                                      passwordValidator().call(p0),
                                  suffixIcon: GestureDetector(
                                    onTap: () {
                                      signupProvider.isPasswordShow.value =
                                          !signupProvider.isPasswordShow.value;
                                    },
                                    child: isPasswordShow
                                        ? const Icon(
                                            Icons.visibility_outlined,
                                          )
                                        : const Icon(
                                            Icons.visibility_off_outlined,
                                          ),
                                  ),
                                );
                              },
                            ),
                            Gap(AppSize.h28),
                            //* Confirm Password
                            ValueListenableBuilder(
                              valueListenable:
                                  signupProvider.isConfirmPasswordShow,
                              builder: (context, isConfirmPasswordShow, _) {
                                return AppTextFormField(
                                  title: context.l10n.confirmPassword,
                                  hintText: context.l10n.enterYourPassword,
                                  fillColor: AppColors.ffF8F9FA,
                                  obscureText: !signupProvider
                                      .isConfirmPasswordShow.value,
                                  controller:
                                      signupProvider.confirmPasswordController,
                                  validator: (value) =>
                                      ConfirmPasswordValidator(
                                    errorText:
                                        context.l10n.pleaseEnterConfirmPassword,
                                    password: signupProvider
                                        .passwordController.text
                                        .trim(),
                                  ).call(value),
                                  suffixIcon: GestureDetector(
                                    onTap: () {
                                      signupProvider
                                              .isConfirmPasswordShow.value =
                                          !signupProvider
                                              .isConfirmPasswordShow.value;
                                    },
                                    child: isConfirmPasswordShow
                                        ? const Icon(
                                            Icons.visibility_outlined,
                                          )
                                        : const Icon(
                                            Icons.visibility_off_outlined,
                                          ),
                                  ),
                                );
                              },
                            ),
                            //* Create an account
                            AppPadding(
                              top: AppSize.h40,
                              bottom: AppSize.h16,
                              child: AppButton(
                                text: context.l10n.createAccount,
                                onPressed: () {
                                  if (signupProvider.formKeySignup.currentState!
                                      .validate()) {
                                    signupProvider.registerAPICall(
                                      context: context,
                                    );
                                  }
                                },
                              ),
                            ),
                            //* Navigate Login screen
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  context.l10n.alreadyHaveAccount,
                                  textAlign: TextAlign.center,
                                  style: context.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w400,
                                    fontSize: AppSize.sp14,
                                    color: AppColors.black,
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    if (context.canPop()) {
                                      context.pop();
                                    } else {
                                      AppNavigationService
                                          .pushAndRemoveAllPreviousRoute(
                                        context,
                                        AppRoutes.authBase,
                                        isBaseRoute: true,
                                      );
                                    }
                                  },
                                  child: Text(
                                    ' ${context.l10n.logIn}',
                                    textAlign: TextAlign.center,
                                    style:
                                        context.textTheme.bodyLarge?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      fontSize: AppSize.sp14,
                                      color: AppColors.primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            Gap(AppSize.h10),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
