// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';

/// Enum for Driver Checklist Type
enum DriverCheckListType {
  START_STOP_LOCATION,
  DRIVER,
  END_STOP_LOCATION,
}

/// Extension for DriverCheckListType to get display values
extension DriverCheckListTypeExtension on DriverCheckListType {
  /// Get localized text using rootNavKey context
  String get localizedText {
    final l10n = rootNavKey.currentContext!.l10n;
    switch (this) {
      case DriverCheckListType.START_STOP_LOCATION:
        return l10n.startStopLocation;
      case DriverCheckListType.DRIVER:
        return l10n.driver;
      case DriverCheckListType.END_STOP_LOCATION:
        return l10n.endStopLocation;
    }
  }

  /// Get English display text
  String get englishText {
    switch (this) {
      case DriverCheckListType.START_STOP_LOCATION:
        return 'Start Stop Location';
      case DriverCheckListType.DRIVER:
        return 'Driver';
      case DriverCheckListType.END_STOP_LOCATION:
        return 'End Stop Location';
    }
  }

  /// Get Spanish display text
  String get spanishText {
    switch (this) {
      case DriverCheckListType.START_STOP_LOCATION:
        return 'Ubicación de Parada Inicial';
      case DriverCheckListType.DRIVER:
        return 'Conductor';
      case DriverCheckListType.END_STOP_LOCATION:
        return 'Ubicación de Parada Final';
    }
  }

  /// Get localized text based on locale
  String getLocalizedText(String locale) {
    return locale.startsWith('es') ? spanishText : englishText;
  }

  /// Create enum from string value
  static DriverCheckListType? fromString(String? value) {
    if (value == null) return null;
    try {
      return DriverCheckListType.values.firstWhere(
        (e) => e.name == value.toUpperCase(),
      );
    } catch (e) {
      return null;
    }
  }
}

/// Enum for Customer Verification Status
enum CustomerVerificationStatus {
  ACCEPTED,
  PENDING,
}

/// Extension for CustomerVerificationStatus to get display values
extension CustomerVerificationStatusExtension on CustomerVerificationStatus {
  /// Get localized text using rootNavKey context
  String get localizedText {
    final l10n = rootNavKey.currentContext!.l10n;
    switch (this) {
      case CustomerVerificationStatus.ACCEPTED:
        return l10n.accepted;
      case CustomerVerificationStatus.PENDING:
        return l10n.pending;
    }
  }

  /// Get English display text
  String get englishText {
    switch (this) {
      case CustomerVerificationStatus.ACCEPTED:
        return 'Accepted';
      case CustomerVerificationStatus.PENDING:
        return 'Pending';
    }
  }

  /// Get Spanish display text
  String get spanishText {
    switch (this) {
      case CustomerVerificationStatus.ACCEPTED:
        return 'Aceptado';
      case CustomerVerificationStatus.PENDING:
        return 'Pendiente';
    }
  }

  /// Get localized text based on locale
  String getLocalizedText(String locale) {
    return locale.startsWith('es') ? spanishText : englishText;
  }

  /// Create enum from string value
  static CustomerVerificationStatus? fromString(String? value) {
    if (value == null) return null;
    try {
      return CustomerVerificationStatus.values.firstWhere(
        (e) => e.name == value.toUpperCase(),
      );
    } catch (e) {
      return null;
    }
  }
}

/// Enum for Performed By
enum PerformedBy {
  COLLECTING_FROM_CUSTOMER,
  COLLECTING_FROM_ORIGIN_STOP_ADMIN,
  COLLECTING_FROM_DRIVER,
  HANDED_OVER_TO_CUSTOMER,
}

/// Extension for PerformedBy to get display values
extension PerformedByExtension on PerformedBy {
  /// Get localized text using rootNavKey context
  String get localizedText {
    final l10n = rootNavKey.currentContext!.l10n;
    switch (this) {
      case PerformedBy.COLLECTING_FROM_CUSTOMER:
        return l10n.collectingFromCustomer;
      case PerformedBy.COLLECTING_FROM_ORIGIN_STOP_ADMIN:
        return l10n.collectingFromOriginStopAdmin;
      case PerformedBy.COLLECTING_FROM_DRIVER:
        return l10n.collectingFromDriver;
      case PerformedBy.HANDED_OVER_TO_CUSTOMER:
        return l10n.handedOverToCustomer;
    }
  }

  /// Get English display text
  String get englishText {
    switch (this) {
      case PerformedBy.COLLECTING_FROM_CUSTOMER:
        return 'Collecting from Customer';
      case PerformedBy.COLLECTING_FROM_ORIGIN_STOP_ADMIN:
        return 'Collecting from Origin Stop Admin';
      case PerformedBy.COLLECTING_FROM_DRIVER:
        return 'Collecting from Driver';
      case PerformedBy.HANDED_OVER_TO_CUSTOMER:
        return 'Handed Over to Customer';
    }
  }

  /// Get Spanish display text
  String get spanishText {
    switch (this) {
      case PerformedBy.COLLECTING_FROM_CUSTOMER:
        return 'Recogiendo del Cliente';
      case PerformedBy.COLLECTING_FROM_ORIGIN_STOP_ADMIN:
        return 'Recogiendo del Administrador de Parada de Origen';
      case PerformedBy.COLLECTING_FROM_DRIVER:
        return 'Recogiendo del Conductor';
      case PerformedBy.HANDED_OVER_TO_CUSTOMER:
        return 'Entregado al Cliente';
    }
  }

  /// Get localized text based on locale
  String getLocalizedText(String locale) {
    return locale.startsWith('es') ? spanishText : englishText;
  }

  /// Create enum from string value
  static PerformedBy? fromString(String? value) {
    if (value == null) return null;
    try {
      return PerformedBy.values.firstWhere(
        (e) => e.name == value.toUpperCase(),
      );
    } catch (e) {
      return null;
    }
  }
}
