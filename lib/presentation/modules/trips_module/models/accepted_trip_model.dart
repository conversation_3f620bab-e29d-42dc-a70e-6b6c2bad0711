import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/sent_offer_model.dart';

class AcceptedTripFullModel {
  AcceptedTripFullModel({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  factory AcceptedTripFullModel.fromJson(Map<String, dynamic> json) =>
      AcceptedTripFullModel(
        count: json['count'] as int?,
        next: json['next'] as String?,
        previous: json['previous'] as String?,
        results: json['results'] == null
            ? []
            : List<AcceptedTripModelData>.from(
                (json['results'] as List?)?.map(
                      (x) => AcceptedTripModelData.fromJson(
                        x as Map<String, dynamic>,
                      ),
                    ) ??
                    [],
              ),
      );
  int? count;
  String? next;
  String? previous;
  List<AcceptedTripModelData>? results;

  Map<String, dynamic> toJson() => {
        'count': count,
        'next': next,
        'previous': previous,
        'results': results == null
            ? []
            : List<dynamic>.from(results!.map((x) => x.toJson())),
      };
}

class AcceptedTripModelData {
  AcceptedTripModelData({
    this.id,
    this.startStopLocation,
    this.endStopLocation,
    this.driver,
    this.intermediatePickUpPoint,
    this.spotAvailableForReservation,
    this.tripStartDate,
    this.tripEndDate,
    this.status,
    this.costPerKilometer,
    this.deadlineDate,
    this.completedAt,
    this.totalTripDistance,
    this.tripType,
    this.tripId,
    this.ratings,
    this.totalBookedSlots,
    this.exclusiveTrips,
    this.equipment,
  });

  factory AcceptedTripModelData.fromJson(Map<String, dynamic> json) =>
      AcceptedTripModelData(
        id: json['id'] as int?,
        startStopLocation: json['start_stop_location'] == null ||
                json['start_stop_location'] is String
            ? null
            : StopLocation.fromJson(
                json['start_stop_location'] as Map<String, dynamic>,
              ),
        endStopLocation: json['end_stop_location'] == null ||
                json['end_stop_location'] is String
            ? null
            : StopLocation.fromJson(
                json['end_stop_location'] as Map<String, dynamic>,
              ),
        equipment: json['equipment'] == null
            ? null
            : Equipment.fromJson(json['equipment'] as Map<String, dynamic>),
        totalBookedSlots: json['total_booked_slots'] as num?,
        driver: json['driver'] == null
            ? null
            : Driver.fromJson(json['driver'] as Map<String, dynamic>),
        intermediatePickUpPoint: json['intermediate_pick_up_point'] == null
            ? []
            : List<IntermediatePointModel>.from(
                (json['intermediate_pick_up_point'] as List?)?.map(
                      (x) => IntermediatePointModel.fromJson(
                        x as Map<String, dynamic>,
                      ),
                    ) ??
                    [],
              ),
        spotAvailableForReservation:
            json['spot_available_for_reservation'] as num?,
        tripStartDate: json['trip_start_date'] == null
            ? null
            : DateTime.parse(json['trip_start_date'] as String),
        tripEndDate: json['trip_end_date'] == null
            ? null
            : DateTime.parse(json['trip_end_date'] as String),
        status: json['status'] as String?,
        costPerKilometer: json['cost_per_kilometer'] as num?,
        deadlineDate: json['deadline_date'] == null
            ? null
            : DateTime.parse(json['deadline_date'] as String),
        completedAt: json['completed_at'] == null
            ? null
            : DateTime.parse(json['completed_at'] as String),
        totalTripDistance: json['total_trip_distance'] as num?,
        tripType: json['trip_type'] as String?,
        tripId: json['trip_id'] as String?,
        ratings: json['ratings'] as num?,
        exclusiveTrips: json['exclusive_trips'] == null
            ? null
            : ExclusiveTrips.fromJson(
                json['exclusive_trips'] as Map<String, dynamic>,
              ),
      );
  int? id;
  StopLocation? startStopLocation;
  StopLocation? endStopLocation;
  Equipment? equipment;
  num? totalBookedSlots;
  Driver? driver;
  List<IntermediatePointModel>? intermediatePickUpPoint;
  num? spotAvailableForReservation;
  DateTime? tripStartDate;
  DateTime? tripEndDate;
  String? status;
  num? costPerKilometer;
  DateTime? deadlineDate;
  DateTime? completedAt;
  num? totalTripDistance;
  String? tripType;
  String? tripId;
  num? ratings;
  ExclusiveTrips? exclusiveTrips;

  Map<String, dynamic> toJson() => {
        'id': id,
        'start_stop_location': startStopLocation,
        'end_stop_location': endStopLocation,
        'equipment': equipment?.toJson(),
        'total_booked_slots': totalBookedSlots,
        'driver': driver?.toJson(),
        'intermediate_pick_up_point': intermediatePickUpPoint == null
            ? []
            : List<dynamic>.from(intermediatePickUpPoint!.map((x) => x)),
        'spot_available_for_reservation': spotAvailableForReservation,
        'trip_start_date': tripStartDate?.toIso8601String(),
        'trip_end_date': tripEndDate?.toIso8601String(),
        'status': status,
        'cost_per_kilometer': costPerKilometer,
        'deadline_date': deadlineDate?.toIso8601String(),
        'completed_at': completedAt,
        'total_trip_distance': totalTripDistance,
        'trip_type': tripType,
        'trip_id': tripId,
        'ratings': ratings,
        'exclusive_trips': exclusiveTrips?.toJson(),
      };
}

class Driver {
  Driver({
    this.id,
    this.firstName,
    this.lastName,
  });

  factory Driver.fromJson(Map<String, dynamic> json) => Driver(
        id: json['id'] as int?,
        firstName: json['first_name'] as String?,
        lastName: json['last_name'] as String?,
      );
  int? id;
  String? firstName;
  dynamic lastName;

  Map<String, dynamic> toJson() => {
        'id': id,
        'first_name': firstName,
        'last_name': lastName,
      };
}

class Equipment {
  Equipment({
    this.id,
    this.name,
    this.plateNumber,
    this.slot,
    this.economicNumber,
    this.insurancePolicyNumber,
    this.winch,
  });

  factory Equipment.fromJson(Map<String, dynamic> json) => Equipment(
        id: json['id'] as int?,
        name: json['name'] as String?,
        plateNumber: json['plate_number'] as String?,
        slot: json['slot'] as int?,
        economicNumber: json['economic_number'] as String?,
        insurancePolicyNumber: json['insurance_policy_number'] as String?,
        winch: json['winch'] as bool?,
      );
  int? id;
  String? name;
  String? plateNumber;
  int? slot;
  String? economicNumber;
  String? insurancePolicyNumber;
  bool? winch;

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'plate_number': plateNumber,
        'slot': slot,
        'economic_number': economicNumber,
        'insurance_policy_number': insurancePolicyNumber,
        'winch': winch,
      };
}

class IntermediatePointModel {
  IntermediatePointModel({
    this.id,
    this.stopLocation,
    this.estimatedArrivalDate,
    this.stopLocationIndex,
    this.distance,
    this.isArrived,
    this.arrivedAt,
    this.isMovedToNextStop,
    this.movedAt,
  });

  factory IntermediatePointModel.fromJson(Map<String, dynamic> json) =>
      IntermediatePointModel(
        id: json['id'] as int?,
        stopLocation: json['stop_location'] == null
            ? null
            : StopLocation.fromJson(
                json['stop_location'] as Map<String, dynamic>,
              ),
        estimatedArrivalDate: json['estimated_arrival_date'] == null
            ? null
            : DateTime.parse(json['estimated_arrival_date'] as String),
        stopLocationIndex: json['stop_location_index'] as int?,
        distance: json['distance'] as num?,
        isArrived: json['is_arrived'] as bool?,
        arrivedAt: json['arrived_at'],
        isMovedToNextStop: json['is_moved_to_next_stop'] as bool?,
        movedAt: json['moved_at'],
      );
  int? id;
  StopLocation? stopLocation;
  DateTime? estimatedArrivalDate;
  int? stopLocationIndex;
  num? distance;
  bool? isArrived;
  dynamic arrivedAt;
  bool? isMovedToNextStop;
  dynamic movedAt;

  Map<String, dynamic> toJson() => {
        'id': id,
        'stop_location': stopLocation?.toJson(),
        'estimated_arrival_date': estimatedArrivalDate?.toIso8601String(),
        'stop_location_index': stopLocationIndex,
        'distance': distance,
        'is_arrived': isArrived,
        'arrived_at': arrivedAt,
        'is_moved_to_next_stop': isMovedToNextStop,
        'moved_at': movedAt,
      };
}
