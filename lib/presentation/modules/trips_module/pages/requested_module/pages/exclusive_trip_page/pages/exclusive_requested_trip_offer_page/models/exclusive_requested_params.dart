// ignore_for_file: public_member_api_docs

import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/provider/trip_data_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/provider/requested_trip_provider.dart';

/// Parameters for the ExclusiveRequestedScreen
class ExclusiveRequestedParams {
  /// Constructor
  ExclusiveRequestedParams({
    required this.exclusiveBooking,
    required this.requestedTripProvider,
    required this.tripDataProvider,
    this.tripId,
    this.isEditMode = false,
    this.existingOfferId,
    this.existingEquipmentId,
    this.existingDriverId,
    this.existingSpotAvailable,
    this.existingCostPerKilometer,
    this.existingTotalTripDistance,
  });
  final ExclusiveTrip? exclusiveBooking;
  final int? tripId;
  final TripDataProvider tripDataProvider;
  final RequestedTripProvider requestedTripProvider;

  /// Whether this is edit mode
  final bool isEditMode;

  /// Existing offer ID for editing
  final String? existingOfferId;

  /// Existing equipment ID for pre-selection
  final int? existingEquipmentId;

  /// Existing driver ID for pre-selection
  final int? existingDriverId;

  /// Existing spot available for pre-filling
  final int? existingSpotAvailable;

  /// Existing cost per kilometer for pre-filling
  final num? existingCostPerKilometer;

  /// Existing total trip distance for pre-filling
  final num? existingTotalTripDistance;
}
