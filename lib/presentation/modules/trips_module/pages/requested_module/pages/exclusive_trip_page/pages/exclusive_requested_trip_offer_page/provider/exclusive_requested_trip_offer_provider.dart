import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/app_models/equipment_dropdown_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/places_api_provider_class.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/exclusive_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/pages/exclusive_trip_page/pages/exclusive_requested_trip_offer_page/models/exclusive_requested_params.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_keys.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';

import 'package:transportmatch_provider/utils/logger.dart';

class ExclusiveRequestedTripOfferProvider extends ChangeNotifier {
  ExclusiveRequestedTripOfferProvider({
    required this.exclusiveBooking,
    required this.exclusiveRequestedParams,
  }) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      if (exclusiveRequestedParams.exclusiveBooking == null &&
          exclusiveRequestedParams.tripId != null) {
        await exclusiveRequestedParams.requestedTripProvider
            .getExclusiveTripBookings(exclusiveRequestedParams.tripId!)
            .then(
          (value) {
            value?.toJson().logD;
            exclusiveBooking = value;
            notify();
          },
        );
      } else {
        exclusiveBooking = exclusiveRequestedParams.exclusiveBooking;
        notify();
      }
      if (exclusiveBooking != null) {
        await exclusiveRequestedParams.tripDataProvider.getDropDownListApiCall(
          startDate:
              exclusiveBooking?.customerStartDate?.dateDropDownApiPramOnly ??
                  '',
          endDate:
              exclusiveBooking?.customerEndDate?.dateDropDownApiPramOnly ?? '',
          isShowLoader: isShowLoader,
        );

        // Pre-populate fields if in edit mode
        if (exclusiveRequestedParams.isEditMode) {
          _prePopulateEditFields();
        }
      }
    });
  }
  ExclusiveTrip? exclusiveBooking;
  final ExclusiveRequestedParams exclusiveRequestedParams;
  bool isClosed = false;

  /// Google map function and variables
  CameraPosition googleMapInitial = const CameraPosition(
    target: LatLng(37.42796133580664, -122.085749655962),
    zoom: 14.4746,
  );
  GoogleMapController? googleMapController;
  List<Marker> markers = <Marker>[];
  ValueNotifier<List<Polyline>> polyline = ValueNotifier(<Polyline>[]);
  ValueNotifier<int> distance = ValueNotifier(0);
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  CancelToken? getTripDetailsCancelToken;

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  /// Pre-populate fields when in edit mode
  void _prePopulateEditFields() {
    final tripDataProvider = exclusiveRequestedParams.tripDataProvider;

    // Pre-select equipment if provided
    if (exclusiveRequestedParams.existingEquipmentId != null) {
      final equipment = tripDataProvider.equipmentListModelList.value
          .where((e) => e.id == exclusiveRequestedParams.existingEquipmentId)
          .firstOrNull;
      if (equipment != null) {
        tripDataProvider.selectedEquipment.setDropDown(
          DropDownValueModel(
            name: equipment.name ?? '',
            value: equipment,
          ),
        );
      }
    }

    // Pre-select driver if provided
    if (exclusiveRequestedParams.existingDriverId != null) {
      final driver = tripDataProvider.driverListModelList.value
          .where((e) => e.id == exclusiveRequestedParams.existingDriverId)
          .firstOrNull;
      if (driver != null) {
        tripDataProvider.selectedDriver.setDropDown(
          DropDownValueModel(
            name: '${driver.firstName} ${driver.lastName}',
            value: driver,
          ),
        );
      }
    }

    // Pre-fill spot available if provided
    if (exclusiveRequestedParams.existingSpotAvailable != null &&
        exclusiveRequestedParams.existingEquipmentId != null &&
        tripDataProvider.selectedEquipment.dropDownValue != null) {
      if (((tripDataProvider.selectedEquipment.dropDownValue?.value
                      as EquipmentDropDownModel?)
                  ?.id ??
              0) ==
          exclusiveRequestedParams.existingEquipmentId) {
        exclusiveRequestedParams.requestedTripProvider.spotAvail.text =
            (tripDataProvider.selectedEquipment.dropDownValue?.value
                        as EquipmentDropDownModel?)
                    ?.slot
                    ?.toString() ??
                '';
      }
    }

    // Pre-fill cost per kilometer if provided
    if (exclusiveRequestedParams.existingCostPerKilometer != null) {
      exclusiveRequestedParams.requestedTripProvider.costPerKmController.text =
          exclusiveRequestedParams.existingCostPerKilometer.toString();
    }

    // Pre-fill total trip distance if provided
    if (exclusiveRequestedParams.existingTotalTripDistance != null) {
      distance.value =
          (exclusiveRequestedParams.existingTotalTripDistance! * 1000).toInt();
    }

    // Pre-fill total cost if provided
    if (exclusiveRequestedParams.existingCostPerKilometer != null &&
        exclusiveRequestedParams.existingTotalTripDistance != null) {
      exclusiveRequestedParams.requestedTripProvider.totalCostController.text =
          (exclusiveRequestedParams.existingCostPerKilometer! *
                  exclusiveRequestedParams.existingTotalTripDistance!)
              .toString();
    }
  }

  Future<void> onMapCreated(
    GoogleMapController controller, {
    required ExclusiveTrip newAssignData,
  }) async {
    try {
      if (isClosed) return;
      googleMapController = controller;
      markers = [];
      polyline.value = [];
      await addGoogleMapMarker(
        LatLng(
          double.parse(
            newAssignData.userStartLocation?.latitude ?? '37.42796133580664',
          ),
          double.parse(
            newAssignData.userStartLocation?.longitude ?? '-122.085749655962',
          ),
        ),
        ApiKeys.start,
      );
      await addGoogleMapMarker(
        LatLng(
          double.parse(
            newAssignData.userEndLocation?.latitude ?? '37.42796133580664',
          ),
          double.parse(
            newAssignData.userEndLocation?.longitude ?? '-122.085749655962',
          ),
        ),
        ApiKeys.end,
      );
    } catch (e) {
      e.toString().logE;
    }
  }

  /// to add google map marker
  Future<void> addGoogleMapMarker(
    LatLng latLng,
    String id, {
    bool isRemove = false,
  }) async {
    if (isClosed) return;
    if (isRemove) {
      markers.removeWhere((element) => element.markerId.value == id);
    } else {
      markers
        ..removeWhere((element) => element.markerId.value == id)
        ..add(
          Marker(
            markerId: MarkerId(id),
            position: latLng,
          ),
        );
      await fitBounds(latLng);
    }
    notify();
  }

  /// to fit bounds
  Future<void> fitBounds(LatLng lat) async {
    if (googleMapController == null || isClosed) return;
    if (markers.length < 2) {
      await googleMapController!
          .moveCamera(CameraUpdate.newLatLngZoom(lat, 10));
      return;
    }

    final bounds = PlaceApiProvider().getLatLngBounds(
      markers.map((e) => e.position).toList(),
    );
    final latList = await PlaceApiProvider().getRouteList(
      origin: LatLng(
        markers[0].position.latitude,
        markers[0].position.longitude,
      ),
      destination: LatLng(
        markers[1].position.latitude,
        markers[1].position.longitude,
      ),
    );
    if (googleMapController == null || isClosed) return;
    await googleMapController!
        .animateCamera(CameraUpdate.newLatLngBounds(bounds, 50))
        .then((v) {
      polyline.value.clear();
      polyline.value = [
        Polyline(
          polylineId: const PolylineId('polyline'),
          points: latList.routeList
              .map((e) => LatLng(e.latitude, e.longitude))
              .toList(),
          color: AppColors.ff0087C7,
        ),
      ];
      distance.value = latList.distance.toInt();
      notify();
    });
  }

  /// This function is to create exclusive trip offer
  CancelToken? createExclusiveTripOfferCancelToken;

  /// This API is called for creating exclusive trip offer
  Future<void> createExclusiveTripOffer(
    BuildContext context, {
    required DateTime startDate,
    required DateTime endDate,
    required int equipmentId,
    required int driverId,
    required int spotAvailableForReservation,
    required int totalTripDistance,
    required double costPerKilometer,
    required int bookingId,
  }) async {
    if (equipmentId == 0) {
      context.l10n.pleaseSelectEquipment.showErrorAlert();
    } else if (spotAvailableForReservation <= 0) {
      context.l10n.reservationSpotGreaterThanZero.showErrorAlert();
    } else if (driverId == 0) {
      context.l10n.pleaseSelectDriver.showErrorAlert();
    } else if (costPerKilometer <= 0) {
      context.l10n.costPerKmGreaterThanZero.showErrorAlert();
    } else if (totalTripDistance <= 0) {
      context.l10n.tripDistanceGreaterThanZero.showErrorAlert();
    } else {
      createExclusiveTripOfferCancelToken?.cancel();
      createExclusiveTripOfferCancelToken = CancelToken();
      isShowLoader.value = true;
      try {
        final result =
            await Injector.instance<TripRepository>().createExclusiveTripOffer(
          ApiRequest(
            path: EndPoints.createExclusiveTripOffer,
            cancelToken: createExclusiveTripOfferCancelToken,
            data: {
              'equipment': equipmentId,
              'driver': driverId,
              'trip_start_date': startDate.toUtc().toIso8601String(),
              'trip_end_date': endDate.toUtc().toIso8601String(),
              'spot_available_for_reservation': spotAvailableForReservation,
              'total_trip_distance': totalTripDistance ~/ 1000,
              'cost_per_kilometer': costPerKilometer,
              // if (date.value != null)
              //   'deadline_date': date.value!.toUtc().toIso8601String(),
              'booking': bookingId,
            },
          ),
        );

        await result.when(
          success: (data) async {
            if ((createExclusiveTripOfferCancelToken?.isCancelled ?? true) ||
                isClosed) {
              return;
            }
            context.l10n.offerSentSuccessfully.showSuccessAlert();
            AppNavigationService.pop(context);
            isShowLoader.value = false;
            notify();
          },
          error: (exception) async {
            isShowLoader.value = false;
            exception.message.showErrorAlert();
          },
        );
      } catch (e) {
        isShowLoader.value = false;
        if ((createExclusiveTripOfferCancelToken?.isCancelled ?? true) ||
            isClosed) {
          return;
        }
        '======>>>> create Exclusive Trip Offer error $e'.logE;
      }
    }
  }

  /// This function is to update exclusive trip offer
  CancelToken? updateExclusiveTripOfferCancelToken;

  /// This API is called for updating exclusive trip offer
  Future<void> updateExclusiveTripOffer(
    BuildContext context, {
    required int equipmentId,
    required int driverId,
    required int spotAvailableForReservation,
    required String tripId,
  }) async {
    if (equipmentId == 0) {
      context.l10n.pleaseSelectEquipment.showErrorAlert();
    } else if (spotAvailableForReservation <= 0) {
      context.l10n.reservationSpotGreaterThanZero.showErrorAlert();
    } else if (spotAvailableForReservation >
        ((exclusiveRequestedParams.tripDataProvider.selectedEquipment
                    .dropDownValue?.value as EquipmentDropDownModel?)
                ?.slot ??
            0)) {
      context.l10n
          .pleaseEnterValidSlot(
            (exclusiveRequestedParams.tripDataProvider.selectedEquipment
                        .dropDownValue?.value as EquipmentDropDownModel?)
                    ?.slot ??
                0,
          )
          .showErrorAlert();
    } else if (driverId == 0) {
      context.l10n.pleaseSelectDriver.showErrorAlert();
    } else {
      updateExclusiveTripOfferCancelToken?.cancel();
      updateExclusiveTripOfferCancelToken = CancelToken();
      isShowLoader.value = true;
      try {
        final result =
            await Injector.instance<TripRepository>().updateExclusiveTripOffer(
          ApiRequest(
            path: EndPoints.updateExclusiveTripOffer(tripId),
            cancelToken: updateExclusiveTripOfferCancelToken,
            data: {
              'equipment': equipmentId,
              'driver': driverId,
              'spot_available_for_reservation': spotAvailableForReservation,
            },
          ),
        );

        await result.when(
          success: (data) async {
            if ((updateExclusiveTripOfferCancelToken?.isCancelled ?? true) ||
                isClosed) {
              return;
            }
            context.l10n.tripUpdatedSuccess.showSuccessAlert();
            AppNavigationService.pop(context);
            isShowLoader.value = false;
            notify();
          },
          error: (exception) async {
            isShowLoader.value = false;
            exception.message.showErrorAlert();
          },
        );
      } catch (e) {
        isShowLoader.value = false;
        if ((updateExclusiveTripOfferCancelToken?.isCancelled ?? true) ||
            isClosed) {
          return;
        }
        '======>>>> update Exclusive Trip Offer error $e'.logE;
      }
    }
  }

  Future<void> getTripDetail(String tripId) async {
    if (isClosed) return;
    try {
      getTripDetailsCancelToken?.cancel();
      getTripDetailsCancelToken = CancelToken();
      isShowLoader.value = true;
      final res = await Injector.instance<TripRepository>().getTripDetail(
        ApiRequest(
          path: EndPoints.getTripDetail(tripId),
          cancelToken: getTripDetailsCancelToken,
        ),
      );
      if (isClosed) return;
      isShowLoader.value = false;
      await res.when(
        success: (data) async {
          data.toJson().logD;
          if (isClosed || (getTripDetailsCancelToken?.isCancelled ?? true)) {
            return;
          }
          // await assignData(data);
        },
        error: (exception) async {
          if (isClosed || (getTripDetailsCancelToken?.isCancelled ?? true)) {
            return;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getTripDetailsCancelToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
      e.toString().logE;
    }
  }

  @override
  void dispose() {
    isClosed = true;
    googleMapController?.dispose();
    googleMapController = null;
    markers.clear();
    polyline.dispose();
    distance.dispose();
    isShowLoader.dispose();
    createExclusiveTripOfferCancelToken?.cancel();
    updateExclusiveTripOfferCancelToken?.cancel();
    super.dispose();
  }
}
