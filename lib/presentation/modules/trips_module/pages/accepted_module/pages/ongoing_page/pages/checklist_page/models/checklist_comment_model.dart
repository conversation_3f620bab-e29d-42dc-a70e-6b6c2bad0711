// ignore_for_file: public_member_api_docs

/// Model for creating checklist comment
class ChecklistCommentModel {
  /// Constructor
  ChecklistCommentModel({
    required this.checklist,
    required this.description,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() => {
        'checklist': checklist,
        'description': description,
      };

  /// The checklist ID
  final int checklist;

  /// The comment description
  final String description;
}

/// Model for checklist comment response
class ChecklistCommentResponseModel {
  /// Constructor
  ChecklistCommentResponseModel({
    this.id,
    this.checklist,
    this.description,
    this.createdAt,
  });

  /// Create from JSON
  factory ChecklistCommentResponseModel.fromJson(Map<String, dynamic> json) =>
      ChecklistCommentResponseModel(
        id: json['id'] as int?,
        checklist: json['checklist'] as int?,
        description: json['description'] as String?,
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at'] as String),
      );

  /// Convert to JSON
  Map<String, dynamic> toJson() => {
        if (id != null) 'id': id,
        if (checklist != null) 'checklist': checklist,
        if (description != null) 'description': description,
        if (createdAt != null) 'created_at': createdAt?.toIso8601String(),
      };

  /// The comment ID
  final int? id;

  /// The checklist ID
  final int? checklist;

  /// The comment description
  final String? description;

  /// The creation date
  final DateTime? createdAt;
}
