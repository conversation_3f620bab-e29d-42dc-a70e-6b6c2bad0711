// ignore_for_file: constant_identifier_names

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:transportmatch_provider/di/injector.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/extensions/ext_string_null.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/checklist_api_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/add_checklist_page/models/add_checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/enums/checklist_types_enum.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/models/checklist_comment_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/models/checklist_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/upcoming_page/models/trip_detail_model.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/shared/repositories/account_repository.dart';
import 'package:transportmatch_provider/shared/repositories/trip_repository.dart';
import 'package:transportmatch_provider/shared/rest_api/api_request.dart';
import 'package:transportmatch_provider/shared/rest_api/endpoints.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_string.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';

class CheckListProvider extends ChangeNotifier {
  CheckListProvider({
    required this.carId,
    this.booking,
    this.tripDetailsModel,
    bool isLoad = true,
  }) {
    if (isLoad) {
      getCheckList(carId);
    }
  }
  bool isClosed = false;
  String? fuelValue;
  CheckListType? performedDuring;
  // AcceptedTripModelData tripModel;
  final TripDetailsModel? tripDetailsModel;
  BookingModel? booking;
  ChecklistApiModel? checklistModel;
  final refreshController = RefreshController();

  final String carId;
  bool isShowLoader = false;
  bool isVerifyingChecklist = false;
  List<ChecklistApiModel> checkList = <ChecklistApiModel>[];

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  List<ImageModel> imageList = [];

  final mileage = TextEditingController();
  final other = TextEditingController();
  final verificationComment = TextEditingController();
  final angleList = [
    AppStrings.left,
    AppStrings.right,
    AppStrings.front,
    AppStrings.back,
    AppStrings.other,
  ];

  /// to get checklist as per vehicle
  CancelToken? getCheckListToken;
  String? nxtUrl;
  Future<void> getCheckList(String carId, {bool isPagination = false}) async {
    if (isClosed) return;
    isShowLoader = true;
    getCheckListToken?.cancel();
    getCheckListToken = CancelToken();
    notify();
    try {
      final res = await Injector.instance<TripRepository>().getCheckList(
        ApiRequest(
          path: nxtUrl ?? EndPoints.getCheckList(carId),
          cancelToken: getCheckListToken,
        ),
      );

      res.when(
        success: (data) {
          if (isClosed || (getCheckListToken?.isCancelled ?? true)) return;
          isShowLoader = false;
          notify();
          nxtUrl = data.next;
          if (isPagination) {
            checkList.addAll(data.results ?? []);
          } else {
            checkList = data.results ?? [];
          }
          notify();
        },
        error: (exception) {
          if (isClosed || (getCheckListToken?.isCancelled ?? true)) return;
          isShowLoader = false;
          notify();
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getCheckListToken?.isCancelled ?? true)) return;
      isShowLoader = false;
      notify();

      '==>> get checklist api error $e'.logE;
    }
  }

  final isCheckListDetailShowLoader = ValueNotifier<bool>(false);
  CancelToken? getCheckListDetailToken;
  Future<ChecklistApiModel?> getCheckListDetail(int checkListId) async {
    if (isClosed) return null;
    final completer = Completer<ChecklistApiModel?>();
    isCheckListDetailShowLoader.value = true;
    getCheckListDetailToken?.cancel();
    getCheckListDetailToken = CancelToken();

    try {
      final res = await Injector.instance<TripRepository>().getChecklistData(
        ApiRequest(
          path: EndPoints.checklistDetail(checkListId),
          cancelToken: getCheckListDetailToken,
        ),
      );

      isCheckListDetailShowLoader.value = false;
      res.when(
        success: (data) {
          if (isClosed || (getCheckListDetailToken?.isCancelled ?? true)) {
            return null;
          }
          completer.complete(data);
        },
        error: (exception) {
          if (isClosed || (getCheckListDetailToken?.isCancelled ?? true)) {
            return null;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getCheckListDetailToken?.isCancelled ?? true)) {
        return null;
      }
      isCheckListDetailShowLoader.value = false;
      '==>> get checklist data api error $e'.logE;
    }
    return completer.future;
  }

  CancelToken? getLatestCheckListToken;
  Future<ChecklistApiModel?> getLatestCheckListDetail(
    int checkListId, {
    required BuildContext context,
    required CheckListProvider checkListProvider,
  }) async {
    if (isClosed) return null;
    isCheckListDetailShowLoader.value = true;
    getLatestCheckListToken?.cancel();
    getLatestCheckListToken = CancelToken();
    final completer = Completer<ChecklistApiModel?>();

    try {
      final res = await Injector.instance<TripRepository>().getChecklistData(
        ApiRequest(
          path: EndPoints.latestChecklist(checkListId),
          cancelToken: getLatestCheckListToken,
        ),
      );

      res.when(
        success: (data) {
          data.toJson().logE;
          if (isClosed || (getLatestCheckListToken?.isCancelled ?? true)) {
            isCheckListDetailShowLoader.value = false;
            return null;
          }
          performedDuring = checkList.isEmpty
              ? CheckListType.COLLECTING_FROM_ORIGIN_STOP_ADMIN
              : CheckListType.HANDED_OVER_TO_DESTINATION_STOP_ADMIN;
          fuelValue = data.fuelLevel;

          addCheckListData(
            context,
            carInfo: data.bookedCar,
            clientName: data.user?.firstName ?? '',
            isExclusive: false,
            checkListProvider: checkListProvider,
            checklistApiData: data,
            isRedirect: false,
            isClear: false,
          );
          completer.complete(data);
        },
        error: (exception) {
          if (isClosed || (getLatestCheckListToken?.isCancelled ?? true)) {
            return null;
          }
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (getLatestCheckListToken?.isCancelled ?? true)) {
        return null;
      }
      isCheckListDetailShowLoader.value = false;
      '==>> get latest checklist data api error $e'.logE;
    }
    isCheckListDetailShowLoader.value = false;
    return completer.future;
  }

  /// this function is used know if add checklist
  /// button is enabled or not
  bool isAddCheckListButtonDisable({
    required bool isExclusive,
    // required bool isOutForDelivery,
    required String? carStatus,
  }) {
    // final isGreater10 = (checkList.firstOrNull?.createdAt ?? DateTime.now())
    //         .toLocal()
    //         .difference(DateTime.now())
    //         .inMinutes >=
    //     10;

    return
        // isGreater10 ||
        // (isExclusive &&
        //     (checkList.firstOrNull?.customerChecklistVerificationStatus ==
        //             ChecklistStatus.ACCEPTED.name &&
        //         isOutForDelivery)) ||
        !(isExclusive
            ? (carStatus == VehicleStatus.HANDED_OVER_TO_DRIVER.name ||
                carStatus == VehicleStatus.CONFIRMED.name)
            : (carStatus == VehicleStatus.RECEIVED_BY_ORIGIN_STOP_ADMIN.name ||
                carStatus == VehicleStatus.HANDED_OVER_TO_DRIVER.name));
  }

  /// create checklist for vehicle
  CancelToken? createCheckListToken;
  CancelToken? verifyChecklistToken;
  Future<void> createCheckList(
    BuildContext context, {
    required bool isExclusive,
  }) async {
    if (isClosed) return;
    createCheckListToken?.cancel();
    createCheckListToken = CancelToken();
    if (performedDuring == null) {
      context.l10n.selectChecklistType.showErrorAlert();
    } else if (mileage.text.isEmpty) {
      context.l10n.enterVehicleMileage.showErrorAlert();
    } else if (fuelValue == null) {
      context.l10n.enterVehicleFuelLevel.showErrorAlert();
    } else if (imageList.isEmpty) {
      context.l10n.addVehicleImages.showErrorAlert();
    } else if (angleList.any(
      (e) => !imageList.any((element) => e == element.angle?.toLowerCase()),
    )) {
      final angle = angleList.firstWhere(
        (e) => !imageList.any((element) => e == element.angle?.toLowerCase()),
      );
      'Please add vehicle $angle side image'.showErrorAlert();
    } else {
      notify();

      final frontImg = await _generateEmptyListImages(
        imgList: _getFileImageList(AppStrings.front),
      )
        ..addAll(_getUrlImageList(AppStrings.front));

      final backImg = await _generateEmptyListImages(
        imgList: _getFileImageList(AppStrings.back),
      )
        ..addAll(_getUrlImageList(AppStrings.back));

      final leftImg = await _generateEmptyListImages(
        imgList: _getFileImageList(AppStrings.left),
      )
        ..addAll(_getUrlImageList(AppStrings.left));

      final rightImg = await _generateEmptyListImages(
        imgList: _getFileImageList(AppStrings.right),
      )
        ..addAll(_getUrlImageList(AppStrings.right));

      final otherImg = await _generateEmptyListImages(
        imgList: _getFileImageList(AppStrings.other),
      )
        ..addAll(_getUrlImageList(AppStrings.other));

      '==>> leftImg $leftImg == ${_getFileImageList(AppStrings.left)}'.logE;
      '==>> rightImg $rightImg == ${_getFileImageList(AppStrings.right)}'.logE;
      '==>> frontImg $frontImg == ${_getFileImageList(AppStrings.front)}'.logE;
      '==>> backImg $backImg == ${_getFileImageList(AppStrings.back)}'.logE;
      '==>> otherImg $otherImg == ${_getFileImageList(AppStrings.other)}'.logE;

      final exList =
          checkDataList.firstWhere((e) => e['title'] == 'Exterior')['list']
              as List<CheckListModel>;
      final inList =
          checkDataList.firstWhere((e) => e['title'] == 'Interior')['list']
              as List<CheckListModel>;
      final accList =
          checkDataList.firstWhere((e) => e['title'] == 'Accessories')['list']
              as List<CheckListModel>;
      final dmList =
          checkDataList.firstWhere((e) => e['title'] == 'Damage / Wear')['list']
              as List<CheckListModel>;

      checklistModel = ChecklistApiModel(
        awsImageKeys: {
          if (rightImg.isNotEmpty)
            'RIGHT': rightImg.where((e) => e.isNotEmptyAndNotNull).toList(),
          if (frontImg.isNotEmpty)
            'FRONT': frontImg.where((e) => e.isNotEmptyAndNotNull).toList(),
          if (leftImg.isNotEmpty)
            'LEFT': leftImg.where((e) => e.isNotEmptyAndNotNull).toList(),
          if (backImg.isNotEmpty)
            'BACK': backImg.where((e) => e.isNotEmptyAndNotNull).toList(),
          if (otherImg.isNotEmpty)
            'OTHER': otherImg.where((e) => e.isNotEmptyAndNotNull).toList(),
        },
        mileage: num.parse(mileage.text),
        fuelLevel: fuelValue?.toUpperCase(),
        other: other.text,
        carId: carId,
        performedDuring: isExclusive
            ? performedDuring?.name
            : CheckListType.COLLECTING_FROM_ORIGIN_STOP_ADMIN.name,
        mainLights: exList[0].isCheck.value,
        mediumLights: exList[1].isCheck.value,
        stopLightOrTurnSignals: exList[2].isCheck.value,
        radioAntenna: exList[3].isCheck.value,
        pairOfWindShieldsWipers: exList[4].isCheck.value,
        rightSideMirror: exList[5].isCheck.value,
        sideWindows: exList[6].isCheck.value,
        windshield: exList[7].isCheck.value,
        rearWindow: exList[8].isCheck.value,
        fourWheelCaps: exList[9].isCheck.value,
        bodyWithoutDents: exList[10].isCheck.value,
        frontBumper: exList[11].isCheck.value,
        rearBumper: exList[12].isCheck.value,
        frontLicensePlate: exList[12].isCheck.value,
        rearLicensePlate: exList[13].isCheck.value,
        heating: inList[0].isCheck.value,
        radio: inList[1].isCheck.value,
        speakers: inList[2].isCheck.value,
        lighter: inList[3].isCheck.value,
        rearviewMirror: inList[4].isCheck.value,
        ashtrays: inList[5].isCheck.value,
        seatBelts: inList[6].isCheck.value,
        windowHandles: inList[7].isCheck.value,
        rubberFloors: inList[8].isCheck.value,
        seatCovers: inList[9].isCheck.value,
        doorHandles: inList[10].isCheck.value,
        holder: inList[11].isCheck.value,
        engine: inList[12].isCheck.value,
        floorMats: inList[13].isCheck.value,
        jack: accList[0].isCheck.value,
        wheelWrench: accList[1].isCheck.value,
        toolKit: accList[2].isCheck.value,
        triangle: accList[3].isCheck.value,
        spareTire: accList[4].isCheck.value,
        fireExtinguisher: accList[5].isCheck.value,
        scratchedPaint: dmList[0].isCheck.value,
        brokenWindows: dmList[1].isCheck.value,
        dents: dmList[2].isCheck.value,
        suspension: dmList[3].isCheck.value,
      );
      checklistModel?.toJson().logE;
      try {
        final res = await Injector.instance<TripRepository>().createCheckList(
          ApiRequest(
            path: nxtUrl ?? EndPoints.createCheckList,
            data: checklistModel?.toJson(),
            cancelToken: createCheckListToken,
          ),
        );

        res.when(
          success: (data) {
            if (isClosed || (createCheckListToken?.isCancelled ?? true)) return;

            isShowLoader = false;
            notify();
            context.l10n.checklistCreatedSuccessfully.showSuccessAlert();
            AppNavigationService.pop(context);
            log('==>>>> check list data ${jsonEncode(data)}');
          },
          error: (exception) {
            if (isClosed || (createCheckListToken?.isCancelled ?? true)) return;

            isShowLoader = false;
            notify();
            exception.message.showErrorAlert();
          },
        );
      } catch (e) {
        if (isClosed || (createCheckListToken?.isCancelled ?? true)) return;

        isShowLoader = false;
        notify();
        '==>> assign checklist api error $e'.logE;
      }
      isShowLoader = false;
    }
  }

  /// Verify checklist with comment
  Future<void> verifyCheckList(
    BuildContext context, {
    required int checklistId,
    required bool isExclusive,
  }) async {
    if (isClosed) return;

    // Validate comment
    if (verificationComment.text.trim().isEmpty) {
      context.l10n.addCommentToVerify.showErrorAlert();
      return;
    }

    verifyChecklistToken?.cancel();
    verifyChecklistToken = CancelToken();
    isVerifyingChecklist = true;
    notify();

    try {
      final commentModel = ChecklistCommentModel(
        checklist: checklistId,
        description: verificationComment.text.trim(),
      );

      final res =
          await Injector.instance<TripRepository>().createChecklistComment(
        ApiRequest(
          path: EndPoints.createChecklistComment,
          data: commentModel.toJson(),
          cancelToken: verifyChecklistToken,
        ),
      );

      res.when(
        success: (data) {
          if (isClosed || (verifyChecklistToken?.isCancelled ?? true)) return;

          isVerifyingChecklist = false;
          notify();
          context.l10n.verifiedSuccessfully.showSuccessAlert();
          AppNavigationService.pop(context);
        },
        error: (exception) {
          if (isClosed || (verifyChecklistToken?.isCancelled ?? true)) return;

          isVerifyingChecklist = false;
          notify();
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (verifyChecklistToken?.isCancelled ?? true)) return;

      isVerifyingChecklist = false;
      notify();
      '==>> verify checklist api error $e'.logE;
    }
  }

  List<File> _getFileImageList(String site) {
    return imageList
        .where(
          (e) =>
              e.angle?.toLowerCase() == site && !(e.imageUrl?.isUrl ?? false),
        )
        .map((e) => File(e.imageUrl ?? ''))
        .toList();
  }

  List<String> _getUrlImageList(String site) {
    return imageList
        .where(
          (e) =>
              e.angle?.toLowerCase() == site.toLowerCase() &&
              (e.imageUrl?.isUrl ?? false),
        )
        .map((e) => e.awsImageKey ?? '')
        .toList();
  }

  Future<List<String>> _generateEmptyListImages({
    required List<File> imgList,
  }) async {
    if (isClosed) return [];

    try {
      isShowLoader = true;
      final imagesList = <String>[];
      final data = FormData();
      data.fields.addAll([
        const MapEntry('file_extension', 'jpg'),
        MapEntry('folder_name', ImageTypes.CHECKLIST.name),
        MapEntry('number_of_url', imgList.length.toString()),
      ]);

      final request = ApiRequest(
        path: EndPoints.generateUrl,
        data: data,
      );

      final res =
          await Injector.instance<AccountRepository>().generateUrl(request);

      await res.when(
        success: (data) async {
          if (isClosed) return null;
          final uploadFutures = <Future<void>>[];

          // Loop through the picked images
          if (imgList.length == data.length) {
            for (var i = 0; i < imgList.length; i++) {
              uploadFutures.add(
                _uploadFile(
                  byteImages: imgList[i].readAsBytesSync(),
                  url: data[i]['put_url'].toString(),
                  mimeType: 'image/jpeg',
                ),
              );
            }
          } else {
            rootNavKey.currentContext!.l10n.imagesNotUploaded.showInfoAlert();
            return [];
          }

          // Wait for all uploads to complete
          await Future.wait(uploadFutures);

          // Convert data['keys'] to List<String> and add to imagesList
          imagesList.addAll(
            data.map((e) => e['key_name'].toString()),
          );

          return imagesList;
        },
        error: (exception) {
          if (isClosed) return null;
          exception.message.showErrorAlert();
        },
      );
      return imagesList;
    } catch (e) {
      if (isClosed) return [];
      isShowLoader = false;
      '==>> generateEmptyListImages error $e'.logE;
      e.toString().logE;
      return [];
    }
  }

  Future<bool> _uploadFile({
    required String url,
    required Uint8List byteImages,
    required String mimeType,
    void Function(double)? onSendProgress,
    CancelToken? cancelToken,
  }) {
    try {
      final baseOptions = BaseOptions(
        connectTimeout: const Duration(minutes: 10),
        sendTimeout: const Duration(minutes: 10),
        receiveTimeout: const Duration(minutes: 10),
        // contentType: mimeType,
        headers: {
          'Content-type': '',
        },
      );

      final dio = Dio(baseOptions);
      return dio.put<Map<String, dynamic>>(
        url,
        data: byteImages,
        cancelToken: cancelToken,
        onSendProgress: (count, total) {
          final progressPercent = count / total;
          onSendProgress?.call(progressPercent);
        },
      ).then(
        (value) {
          if (isClosed) return false;
          notify();
          return true;
        },
        onError: (Object error) {
          isShowLoader = false;
          '======= image url error == here == $error'.logE;
          if (error is DioException) {
            if (error.type == DioExceptionType.cancel) {
              return false;
            }
          }
          return false;
        },
      ).onError((error, stackTrace) {
        isShowLoader = false;
        '======= image url error $error'.logE;
        return false;
      });
    } catch (e) {
      isShowLoader = false;
      '==>> _uploadFile error $e'.logE;
      return Future.value(false);
    }
  }

  /// below function is used to remove checklist image
  void removeChecklistImage(int index, String site) {
    imageList
        .where(
          (element) {
            return element.angle == site.toUpperCase() && element.id == index;
          },
        )
        .first
        .isRemove = true;
    if (!imageList.any((e) => !e.isRemove)) {
      imageList.clear();
    }
    notify();
  }

  /// below function is used to add checklist image
  Future<void> addChecklistImage(String site, int length) async {
    if (isClosed) return;
    try {
      final imgList = await AppCommonFunctions.showImagePickerPopup(
            context: rootNavKey.currentContext!,
          ) ??
          [];

      if (imgList.isNotEmpty) {
        final remainingSlots = 7 - length - 1;

        if (remainingSlots > 0) {
          // Add only up to the remaining slots
          final imagesToAdd = imgList.take(remainingSlots).toList();
          for (var i = 0; i < imagesToAdd.length; i++) {
            imageList.add(
              ImageModel(
                angle: site.toUpperCase(),
                id: length + i + 1,
                imageUrl: imagesToAdd[i].path,
              ),
            );
          }
        } else {
          // ignore: use_build_context_synchronously
          rootNavKey.currentContext!.l10n.maxImagesError.showInfoAlert();
        }
      }

      notify();
    } catch (e) {
      '==>> addChecklistImage error $e'.logE;
    }
  }

  /// check list static variables list
  /// +++>>>> DO NOT MODIFY <<<<+++
  List<Map<String, dynamic>> checkDataList = [];
  void addCheckListData(
    BuildContext context, {
    ChecklistApiModel? checklistApiData,
    required BookedCar? carInfo,
    required String clientName,
    required bool isExclusive,
    required CheckListProvider checkListProvider,
    bool isRedirect = true,
    bool isAdd = false,
    bool isClear = true,
  }) {
    if (isClosed) return;
    try {
      checkDataList.clear();
      imageList.clear();
      mileage.clear();
      if (isClear) {
        fuelValue = null;
        performedDuring = null;
      }
      mileage.clear();
      other.clear();
      isShowLoader = false;

      checkDataList = [
        {
          'title': 'Exterior',
          'list': <CheckListModel>[
            CheckListModel(
              'Main Lights',
              ValueNotifier(checklistApiData?.mainLights ?? false),
              value: checklistApiData?.mainLights,
            ),
            CheckListModel(
              'Medium Lights',
              ValueNotifier(checklistApiData?.mediumLights ?? false),
              value: checklistApiData?.mediumLights,
            ),
            CheckListModel(
              'Stop Light/Turn Signals',
              ValueNotifier(checklistApiData?.stopLightOrTurnSignals ?? false),
              value: checklistApiData?.stopLightOrTurnSignals,
            ),
            CheckListModel(
              'Radio Antenna',
              ValueNotifier(checklistApiData?.radioAntenna ?? false),
              value: checklistApiData?.radioAntenna,
            ),
            CheckListModel(
              'Pair of Windshield Wipers',
              ValueNotifier(checklistApiData?.pairOfWindShieldsWipers ?? false),
              value: checklistApiData?.pairOfWindShieldsWipers,
            ),
            CheckListModel(
              'Right Side Mirror',
              ValueNotifier(checklistApiData?.rightSideMirror ?? false),
              value: checklistApiData?.rightSideMirror,
            ),
            CheckListModel(
              'Side Windows',
              ValueNotifier(checklistApiData?.sideWindows ?? false),
              value: checklistApiData?.sideWindows,
            ),
            CheckListModel(
              'Windshield',
              ValueNotifier(checklistApiData?.windshield ?? false),
              value: checklistApiData?.windshield,
            ),
            CheckListModel(
              'Rear Windows',
              ValueNotifier(checklistApiData?.rearWindow ?? false),
              value: checklistApiData?.rearWindow,
            ),
            CheckListModel(
              '4 Wheels Caps',
              ValueNotifier(checklistApiData?.fourWheelCaps ?? false),
              value: checklistApiData?.fourWheelCaps,
            ),
            CheckListModel(
              'Body Without Dents',
              ValueNotifier(checklistApiData?.bodyWithoutDents ?? false),
              value: checklistApiData?.bodyWithoutDents,
            ),
            CheckListModel(
              'Front Bumper',
              ValueNotifier(checklistApiData?.frontBumper ?? false),
              value: checklistApiData?.frontBumper,
            ),
            CheckListModel(
              'Rear Bumper',
              ValueNotifier(checklistApiData?.rearBumper ?? false),
              value: checklistApiData?.rearBumper,
            ),
            CheckListModel(
              'Front License Plate',
              ValueNotifier(checklistApiData?.frontLicensePlate ?? false),
              value: checklistApiData?.frontLicensePlate,
            ),
            CheckListModel(
              'Rear License Plate',
              ValueNotifier(checklistApiData?.rearLicensePlate ?? false),
              value: checklistApiData?.rearLicensePlate,
            ),
          ],
        },
        {
          'title': 'Interior',
          'list': <CheckListModel>[
            CheckListModel(
              'Heating',
              ValueNotifier(checklistApiData?.heating ?? false),
              value: checklistApiData?.heating,
            ),
            CheckListModel(
              'Radio',
              ValueNotifier(checklistApiData?.radio ?? false),
              value: checklistApiData?.radio,
            ),
            CheckListModel(
              'Speakers',
              ValueNotifier(checklistApiData?.speakers ?? false),
              value: checklistApiData?.speakers,
            ),
            CheckListModel(
              'Lighter',
              ValueNotifier(checklistApiData?.lighter ?? false),
              value: checklistApiData?.lighter,
            ),
            CheckListModel(
              'Rearview Mirror',
              ValueNotifier(checklistApiData?.rearviewMirror ?? false),
              value: checklistApiData?.rearviewMirror,
            ),
            CheckListModel(
              'Ashtrays',
              ValueNotifier(checklistApiData?.ashtrays ?? false),
              value: checklistApiData?.ashtrays,
            ),
            CheckListModel(
              'Seat Belt',
              ValueNotifier(checklistApiData?.seatBelts ?? false),
              value: checklistApiData?.seatBelts,
            ),
            CheckListModel(
              'Window Handles',
              ValueNotifier(checklistApiData?.windowHandles ?? false),
              value: checklistApiData?.windowHandles,
            ),
            CheckListModel(
              'Rubber Floors',
              ValueNotifier(checklistApiData?.rubberFloors ?? false),
              value: checklistApiData?.rubberFloors,
            ),
            CheckListModel(
              'Seat Covers',
              ValueNotifier(checklistApiData?.seatCovers ?? false),
              value: checklistApiData?.seatCovers,
            ),
            CheckListModel(
              'Door Handles',
              ValueNotifier(checklistApiData?.doorHandles ?? false),
              value: checklistApiData?.doorHandles,
            ),
            CheckListModel(
              'Holders',
              ValueNotifier(checklistApiData?.holder ?? false),
              value: checklistApiData?.holder,
            ),
            CheckListModel(
              'Engine',
              ValueNotifier(checklistApiData?.engine ?? false),
              value: checklistApiData?.engine,
            ),
            CheckListModel(
              'Floor Mats',
              ValueNotifier(checklistApiData?.floorMats ?? false),
              value: checklistApiData?.floorMats,
            ),
          ],
        },
        {
          'title': 'Accessories',
          'list': <CheckListModel>[
            CheckListModel(
              'Jack',
              ValueNotifier(checklistApiData?.jack ?? false),
              value: checklistApiData?.jack,
            ),
            CheckListModel(
              'Wheel Wrench',
              ValueNotifier(checklistApiData?.wheelWrench ?? false),
              value: checklistApiData?.wheelWrench,
            ),
            CheckListModel(
              'Tool kit',
              ValueNotifier(checklistApiData?.toolKit ?? false),
              value: checklistApiData?.toolKit,
            ),
            CheckListModel(
              'Triangle',
              ValueNotifier(checklistApiData?.triangle ?? false),
              value: checklistApiData?.triangle,
            ),
            CheckListModel(
              'Spare tier',
              ValueNotifier(checklistApiData?.spareTire ?? false),
              value: checklistApiData?.spareTire,
            ),
            CheckListModel(
              'Fire Extinguisher',
              ValueNotifier(checklistApiData?.fireExtinguisher ?? false),
              value: checklistApiData?.fireExtinguisher,
            ),
          ],
        },
        {
          'title': 'Damage / Wear',
          'list': <CheckListModel>[
            CheckListModel(
              'Scratched Paint',
              ValueNotifier(checklistApiData?.scratchedPaint ?? false),
              value: checklistApiData?.scratchedPaint,
            ),
            CheckListModel(
              'Broken Windows',
              ValueNotifier(checklistApiData?.brokenWindows ?? false),
              value: checklistApiData?.brokenWindows,
            ),
            CheckListModel(
              'Dents',
              ValueNotifier(checklistApiData?.dents ?? false),
              value: checklistApiData?.dents,
            ),
            CheckListModel(
              'Suspension',
              ValueNotifier(checklistApiData?.suspension ?? false),
              value: checklistApiData?.suspension,
            ),
          ],
        },
      ];

      for (final site in angleList) {
        imageList
            .addAll(_abstractImageList(site, checklistApiData?.images ?? []));
      }
      checkListProvider.mileage.text =
          checklistApiData?.mileage?.toString() ?? '';
      fuelValue = checklistApiData?.fuelLevel;

      performedDuring = CheckListType.values
          .where((e) => e.name == checklistApiData?.performedDuring)
          .firstOrNull;

      if (isRedirect) {
        AppNavigationService.pushNamed(
          context,
          AppRoutes.tripsAddChecklistScreen,
          extra: AddChecklistParams(
            carInfo: carInfo,
            clientName: clientName,
            checkListProvider: checkListProvider,
            checkListId: checklistApiData?.id,
            isExclusive: isExclusive,
            isAdd: isAdd,
          ),

          // {
          //   'carInfo': carInfo,
          //   'clientName': clientName,
          //   'checkListProvider': checkListProvider,
          //   'checkListId': checklistApiData?.id,
          //   'isExclusive': isExclusive,
          //   'isAdd': isAdd,
          // },
        );
      } else {
        notify();
      }
    } catch (e) {
      '==>> addCheckListData error $e'.logE;
      e.toString().logE;
    }
  }

  List<ImageModel> _abstractImageList(String site, List<ImageModel> imageList) {
    final tempList = imageList
        .where((e) => e.angle?.toLowerCase() == site.toLowerCase())
        .toList();
    final abstractList = <ImageModel>[];
    for (var i = 0; i < tempList.length; i++) {
      abstractList.add(
        ImageModel(
          id: i + 1,
          imageUrl: tempList[i].imageUrl,
          awsImageKey: tempList[i].awsImageKey,
          angle: tempList[i].angle,
        ),
      );
    }
    return abstractList;
  }

  @override
  void dispose() {
    isClosed = true;
    getCheckListToken?.cancel();
    createCheckListToken?.cancel();
    verifyChecklistToken?.cancel();
    mileage.dispose();
    other.dispose();
    verificationComment.dispose();
    refreshController.dispose();
    checkList.clear();
    imageList.clear();
    checkDataList.clear();
    super.dispose();
  }
}
