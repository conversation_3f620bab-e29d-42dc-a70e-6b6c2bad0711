import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/extensions/ext_string.dart';
import 'package:transportmatch_provider/extensions/ext_string_alert.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/booking_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/models/checklist_api_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/add_checklist_page/models/add_checklist_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/enums/checklist_types_enum.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/checklist_page/provider/checklist_provider.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/widgets/checklist_image_widget.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/widgets/checklist_vehicles_info.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/widgets/checklist_widget.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/app_string.dart';
import 'package:transportmatch_provider/utils/enums.dart';
import 'package:transportmatch_provider/utils/logger.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

///Check list Screen Ui
class AddChecklistPage extends StatefulWidget {
  /// Constructor
  const AddChecklistPage({super.key, required this.addChecklistParams});
  final AddChecklistParams addChecklistParams;

  @override
  State<AddChecklistPage> createState() => _AddChecklistPageState();
}

class _AddChecklistPageState extends State<AddChecklistPage> {
  BookedCar? carInfo;
  String clientName = '-';
  bool isExclusive = false;
  ChecklistApiModel? checklistModel;

  @override
  void initState() {
    carInfo = widget.addChecklistParams.carInfo;
    clientName = widget.addChecklistParams.clientName;
    isExclusive = widget.addChecklistParams.isExclusive;
    // checklistModel = widget.addChecklistParams.checklistModel;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.addChecklistParams.checkListId != null &&
          !widget.addChecklistParams.isFromNotification) {
        widget.addChecklistParams.checkListProvider
            .getCheckListDetail(widget.addChecklistParams.checkListId ?? 0)
            .then((value) => checklistModel = value);
      } else {
        /// below function is while notification tapping
        if (widget.addChecklistParams.checkListId != null) {
          widget.addChecklistParams.checkListProvider
              .getCheckListDetail(widget.addChecklistParams.checkListId ?? 0)
              .then(
            (value) {
              if (value != null) {
                value.toJson().logE;
                checklistModel = value;
                clientName = value.user?.firstName ?? '-';
                isExclusive = value.bookedCar?.bookingType ==
                    NotificationType.EXCLUSIVE.name;
                carInfo = value.bookedCar;
                widget.addChecklistParams.checkListProvider.addCheckListData(
                  context,
                  carInfo: value.bookedCar,
                  clientName: clientName,
                  isExclusive: isExclusive,
                  checkListProvider:
                      widget.addChecklistParams.checkListProvider,
                  checklistApiData: value,
                  isRedirect: false,
                );
              }
            },
          );
        }

        /// below function is called when trip is shared trip
        /// and stop admin already create checklist and
        /// now driver is going to add checklist for that
        /// we have to prefill checklist with previous data
        if (widget.addChecklistParams.carInfo?.id != null) {
          widget.addChecklistParams.checkListProvider
              .getLatestCheckListDetail(
            widget.addChecklistParams.carInfo?.id ?? 0,
            context: context,
            checkListProvider: widget.addChecklistParams.checkListProvider,
          )
              .then(
            (value) {
              if (value != null) {
                checklistModel = value;
                clientName = value.user?.firstName ?? '-';
                isExclusive = value.bookedCar?.bookingType ==
                    NotificationType.EXCLUSIVE.name;
                carInfo = value.bookedCar;
              }
            },
          );
        }
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final isAdd = widget.addChecklistParams.isAdd;
    final l10n = context.l10n;
    return ChangeNotifierProvider.value(
      value: widget.addChecklistParams.checkListProvider,
      child: Selector<CheckListProvider, bool>(
        selector: (p0, p1) => p1.isShowLoader,
        builder: (context, isLoading, child) {
          final checkListProvider = context.read<CheckListProvider>();
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(
              title: isAdd ? l10n.addChecklist : l10n.checklist,
            ),
            body: ValueListenableBuilder(
              valueListenable: checkListProvider.isCheckListDetailShowLoader,
              builder: (context, isCheckListDetailShowLoader, _) {
                return AppLoader(
                  isShowLoader: isLoading || isCheckListDetailShowLoader,
                  child: isCheckListDetailShowLoader
                      ? const SizedBox.expand()
                      : child!,
                );
              },
            ),
          );
        },
        child: PopScope(
          onPopInvokedWithResult: (result, _) {
            widget.addChecklistParams.checkListProvider
              ..isShowLoader = false
              ..createCheckListToken?.cancel()
              ..notify();
          },
          child: Builder(
            builder: (context) {
              final checkListProvider = context.read<CheckListProvider>();
              return checklistModel == null &&
                      widget.addChecklistParams.isFromNotification
                  ? Center(child: Text(context.l10n.noChecklistPerformedYet))
                  : SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: context.width,
                            padding: EdgeInsets.all(AppSize.h12),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(AppSize.r5),
                              border: Border.all(color: AppColors.ffADB5BD),
                            ),
                            child: TitleInfoWidget(
                              title: context.l10n.clientName,
                              subTitle: clientName,
                            ),
                          ),
                          Padding(
                            padding:
                                EdgeInsets.symmetric(vertical: AppSize.h10),
                            child: Text(
                              context.l10n.vehiclesInfo,
                              style: context.textTheme.titleLarge?.copyWith(
                                fontSize: AppSize.sp16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                          if (carInfo != null)
                            Padding(
                              padding: EdgeInsets.only(bottom: AppSize.h10),
                              child: ChecklistVehiclesInfo(
                                carBrand: carInfo?.car?.brand,
                                carModel: carInfo?.car?.model,
                                carSerial: carInfo?.serialNumber,
                                carYear: carInfo?.car?.year,
                                carSize: carInfo?.car?.size.toString(),
                                carCondition: carInfo?.carDescription,
                                // plate: carInfo.car?.plate,
                              ),
                            ),
                          if (isExclusive)
                            Text(
                              context.l10n.checklistType,
                              style: context.textTheme.titleLarge?.copyWith(
                                fontSize: AppSize.sp16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          if (isExclusive)
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                if (isExclusive)
                                  CheckListType.COLLECTING_FROM_CUSTOMER
                                else
                                  CheckListType
                                      .COLLECTING_FROM_ORIGIN_STOP_ADMIN,
                                if (isExclusive)
                                  CheckListType.HANDED_OVER_TO_CUSTOMER
                                else
                                  CheckListType
                                      .HANDED_OVER_TO_DESTINATION_STOP_ADMIN,
                              ]
                                  .map(
                                    (e) => Flexible(
                                      child: checkListTypeWidget(
                                        e,
                                        checkListProvider,
                                      ),
                                    ),
                                  )
                                  .toList(),
                            ),
                          Selector<CheckListProvider, CheckListType?>(
                            selector: (p0, p1) => p1.performedDuring,
                            builder: (context, value, child) {
                              final isPickup = value ==
                                      CheckListType.COLLECTING_FROM_CUSTOMER ||
                                  value ==
                                      CheckListType
                                          .COLLECTING_FROM_ORIGIN_STOP_ADMIN;
                              return value != null
                                  ? Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Gap(AppSize.h10),
                                        Padding(
                                          padding: EdgeInsets.only(
                                            bottom: AppSize.h10,
                                          ),
                                          child: AppTextFormField(
                                            title: isPickup
                                                ? context.l10n.mileageAtPickup
                                                : context
                                                    .l10n.mileageAtDelivery,
                                            readOnly: !isAdd,
                                            textAction:TextInputAction.next,
                                            keyboardType: TextInputType.number,
                                            inputFormatters: [
                                              FilteringTextInputFormatter
                                                  .digitsOnly,
                                            ],
                                            controller:
                                                checkListProvider.mileage,
                                          ),
                                        ),
                                        if (checkListProvider
                                                    .tripDetailsModel !=
                                                null &&
                                            checkListProvider.booking != null)
                                          Builder(
                                            builder: (context) {
                                              final tripModel =
                                                  checkListProvider
                                                      .tripDetailsModel;
                                              final booking =
                                                  checkListProvider.booking;
                                              final startLocation = booking
                                                  ?.sharedBookings
                                                  ?.firstOrNull
                                                  ?.intermediateStartStopLocation;
                                              final endLocation = booking
                                                  ?.sharedBookings
                                                  ?.firstOrNull
                                                  ?.intermediateEndStopLocation;
                                              return Padding(
                                                padding: EdgeInsets.only(
                                                  bottom: AppSize.h10,
                                                ),
                                                child: AppTextFormField(
                                                  title: isPickup
                                                      ? context
                                                          .l10n.pickupDatePlace
                                                      : context.l10n
                                                          .deliveryDatePlace,
                                                  readOnly: true,
                                                  textAction:TextInputAction.next,
                                                  controller:
                                                      TextEditingController(
                                                    text: isExclusive
                                                        ? isPickup
                                                            ? '${tripModel?.tripStartDate?.formatDateTimeToLocalDate()?.monthDate} - '
                                                                '(${tripModel?.exclusiveTripData?.userStartLocation?.street})'
                                                            : '${tripModel?.tripEndDate?.formatDateTimeToLocalDate()?.monthDate} - '
                                                                '${tripModel?.exclusiveTripData?.userEndLocation?.street}'
                                                        : isPickup
                                                            ? '${startLocation?.estimatedArrivalDate?.monthDate} - '
                                                                '(${startLocation?.stopLocation?.fullAddress})'
                                                            : '${endLocation?.estimatedArrivalDate?.monthDate} - '
                                                                '(${endLocation?.stopLocation?.fullAddress})',
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                      ],
                                    )
                                  : const SizedBox();
                            },
                          ),
                          Gap(AppSize.h20),
                          ChecklistWidget(
                            checklistModel: checklistModel,
                            isAdd: widget.addChecklistParams.isAdd,
                          ),
                          Text(
                            l10n.fuelLevel,
                            style: context.textTheme.titleLarge?.copyWith(
                              fontSize: AppSize.sp16,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              l10n.low,
                              l10n.half,
                              l10n.full,
                            ]
                                .map((e) => fuelWidget(e, checkListProvider))
                                .toList(),
                          ),
                          if (isAdd ||
                              (checklistModel?.other ?? '').trim().isNotEmpty)
                            AppPadding(
                              top: AppSize.h14,
                              child: AppTextFormField(
                                title: l10n.notes,
                                titleStyle:
                                    context.textTheme.titleLarge?.copyWith(
                                  fontSize: AppSize.sp16,
                                  fontWeight: FontWeight.w700,
                                ),
                                readOnly: !isAdd,
                                textAction:TextInputAction.next,
                                controller: isAdd
                                    ? checkListProvider.other
                                    : TextEditingController(
                                        text: checklistModel?.other?.toString(),
                                      ),
                              ),
                            ),
                          AppPadding(
                            top: AppSize.h20,
                            child: Text(
                              context.l10n.carOwnerComments,
                              style: context.textTheme.titleLarge?.copyWith(
                                fontSize: AppSize.sp16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                          if (checklistModel?.commentList?.isNotEmpty ?? false)
                            ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount:
                                  checklistModel?.commentList?.length ?? 0,
                              padding: EdgeInsets.only(
                                top: AppSize.h5,
                                bottom: AppSize.h5,
                              ),
                              separatorBuilder: (context, index) =>
                                  Gap(AppSize.h5),
                              itemBuilder: (context, index) => Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(AppStrings.arrowWithoutSpace),
                                  Flexible(
                                    child: Text(
                                      checklistModel?.commentList?[index]
                                              .description ??
                                          '',
                                    ),
                                  ),
                                ],
                              ),
                            )
                          else
                            Text(
                              '- ${context.l10n.noCommentsAdded}',
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w400,
                                fontSize: AppSize.sp12,
                                color: AppColors.black,
                              ),
                            ),
                          ChecklistImageWidget(
                            checklistModel: checklistModel,
                            isAdd: widget.addChecklistParams.isAdd,
                          ),
                          Gap(AppSize.h10),
                          if (widget.addChecklistParams.isVerify) ...[
                            AppPadding(
                              top: AppSize.h20,
                              child: AppTextFormField(
                                title: l10n.verificationComment,
                                titleStyle:
                                    context.textTheme.titleLarge?.copyWith(
                                  fontSize: AppSize.sp16,
                                  fontWeight: FontWeight.w700,
                                ),
                                controller:
                                    checkListProvider.verificationComment,
                                maxLine: 3,
                                hintText:
                                    context.l10n.addYourVerificationCommentHere,
                              ),
                            ),
                            Padding(
                              padding:
                                  EdgeInsets.symmetric(vertical: AppSize.h25),
                              child: Selector<CheckListProvider, bool>(
                                selector: (p0, p1) => p1.isVerifyingChecklist,
                                builder: (context, isVerifying, child) {
                                  return AppButton(
                                    text: l10n.verify,
                                    isLoading: isVerifying,
                                    onPressed: isVerifying
                                        ? null
                                        : () =>
                                            checkListProvider.verifyCheckList(
                                              context,
                                              checklistId:
                                                  checklistModel?.id ?? 0,
                                              isExclusive: isExclusive,
                                            ),
                                  );
                                },
                              ),
                            ),
                          ] else if (isAdd)
                            Padding(
                              padding:
                                  EdgeInsets.symmetric(vertical: AppSize.h25),
                              child: AppButton(
                                text: l10n.saveChecklist,
                                onPressed: () =>
                                    checkListProvider.createCheckList(
                                  context,
                                  isExclusive: isExclusive,
                                ),
                              ),
                            )
                          else
                            Gap(AppSize.h15),
                        ],
                      ),
                    );
            },
          ),
        ),
      ),
    );
  }

  Widget fuelWidget(String title, CheckListProvider controller) {
    final isAdd = widget.addChecklistParams.isAdd;
    return Selector<CheckListProvider, String?>(
      selector: (p0, p1) => p1.fuelValue,
      builder: (context, fuelValue, child) {
        final isSelected = !isAdd
            ? title.toLowerCase() == checklistModel?.fuelLevel?.toLowerCase()
            : title.toLowerCase() == controller.fuelValue?.toLowerCase();
        return rowCheckBox(
          onTap: () {
            if (isAdd) {
              controller
                ..fuelValue = title
                ..notify();
            }
          },
          isSelected: isSelected,
          title: title,
        );
      },
    );
  }

  Widget checkListTypeWidget(
    CheckListType title,
    CheckListProvider checkListProvider,
  ) {
    final isAdd = widget.addChecklistParams.isAdd;
    return Selector<CheckListProvider, CheckListType?>(
      selector: (p0, p1) => p1.performedDuring,
      builder: (context, performedDuring, child) {
        final isSelected = !isAdd
            ? title.name == checklistModel?.performedDuring
            : title.name == checkListProvider.performedDuring?.name;
        return rowCheckBox(
          title: switch (title) {
            CheckListType.COLLECTING_FROM_CUSTOMER ||
            CheckListType.COLLECTING_FROM_ORIGIN_STOP_ADMIN =>
              context.l10n.fromPickup,
            _ => context.l10n.fromDrop
          },
          onTap: () {
            if (isAdd) {
              if (checkListProvider.checkList.isEmpty) {
                if (title == CheckListType.COLLECTING_FROM_CUSTOMER ||
                    title == CheckListType.COLLECTING_FROM_ORIGIN_STOP_ADMIN) {
                  checkListProvider
                    ..performedDuring = title
                    ..notify();
                } else {
                  context.l10n.addPickupChecklistFirst.showErrorAlert();
                }
              } else {
                checkListProvider
                  ..performedDuring = title
                  ..notify();
              }
            }
          },
          isSelected: isSelected,
        );
      },
    );
  }

  Widget rowCheckBox({
    required String title,
    required Function() onTap,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: ColoredBox(
        color: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            spacing: AppSize.w6,
            children: [
              CircleAvatar(
                radius: AppSize.r8,
                backgroundColor:
                    isSelected ? AppColors.primaryColor : AppColors.ffADB5BD,
              ),
              Text(
                title,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? AppColors.black : AppColors.ffADB5BD,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
