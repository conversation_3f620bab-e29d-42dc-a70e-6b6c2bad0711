import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/all_shipments_page/models/all_shipments_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/accepted_module/pages/ongoing_page/pages/report_problem_page/models/report_problem_params.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/dashed_line.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/pages/requested_module/widgets/location_info_widget.dart';
import 'package:transportmatch_provider/router/app_navigation_service.dart';
import 'package:transportmatch_provider/router/app_routes.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';

import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

///Ongoing Transporter Card Widgets
class OngoingTransporterCard extends StatelessWidget {
  /// Constructor
  const OngoingTransporterCard({
    super.key,
    required this.data,
  });

  final AcceptedTripModelData data;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r4),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with "Your Shipment" and "Edit"
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.l10n.no_of_vehicles,
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: AppColors.ffADB5BD,
                        fontSize: AppSize.sp12,
                      ),
                    ),
                    SizedBox(height: AppSize.h2),
                    Text(
                      data.totalBookedSlots?.toString() ?? '-',
                      style: context.textTheme.titleLarge?.copyWith(
                        fontSize: AppSize.sp18,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    GestureDetector(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.tripsAllShipmentsScreen,
                        extra: AllShipmentsParams(
                          tripId: data.id,
                        ),
                      ),
                      child: Text(
                        context.l10n.details,
                        style: context.textTheme.titleLarge?.copyWith(
                          color: AppColors.primaryColor,
                          fontSize: AppSize.sp16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.tripsReportProblemScreen,
                        extra: ReportProblemParams(tripId: data.id ?? -1),
                      ),
                      child: Padding(
                        padding: EdgeInsets.only(left: AppSize.h10),
                        child: Text(
                          context.l10n.report,
                          style: context.textTheme.titleLarge?.copyWith(
                            color: Colors.red,
                            fontSize: AppSize.sp16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: AppSize.h10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: AppSize.w4,
                children: [
                  AppAssets.iconsLocationOrigin.image(
                    height: AppSize.h14,
                  ),
                  const Expanded(
                    child: SizedBox(
                      child: Row(
                        children: [
                          Expanded(flex: 65, child: Divider()),
                          Expanded(
                            flex: 35,
                            child: DashedDivider(),
                          ),
                        ],
                      ),
                    ),
                  ),
                  AppAssets.iconsLocation.image(
                    height: AppSize.h14,
                  ),
                ],
              ),
            ),

            Padding(
              padding: EdgeInsets.only(bottom: AppSize.h10),
              child: TitleInfoWidget(
                title: context.l10n.transporter,
                // TODO : Transporter name or Provider name
                subTitle: 'InTrack Transport',
                subTitleFontWeight: FontWeight.w500,
                subTitleFontSize: AppSize.sp16,
              ),
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: 10,
              children: [
                Flexible(
                  child: LocationInfo(
                    title: data.exclusiveTrips?.userStartLocation?.street ??
                        data.startStopLocation?.fullAddress ??
                        '',
                    date: data.startStopLocation?.date?.monthDate ??
                        data.tripStartDate?.monthDate ??
                        '',
                    latitude:
                        data.exclusiveTrips?.userStartLocation?.latitude ??
                            data.startStopLocation?.latitude,
                    longitude:
                        data.exclusiveTrips?.userStartLocation?.longitude ??
                            data.startStopLocation?.longitude,
                    icon: AppAssets.iconsCalender.image(
                      height: AppSize.h10,
                    ),
                  ),
                ),
                Flexible(
                  child: LocationInfo(
                    title: data.exclusiveTrips?.userEndLocation?.street ??
                        data.endStopLocation?.fullAddress ??
                        '',
                    date: data.endStopLocation?.date?.monthDate ??
                        data.tripEndDate?.monthDate ??
                        '',
                    latitude: data.exclusiveTrips?.userEndLocation?.latitude ??
                        data.endStopLocation?.latitude,
                    longitude:
                        data.exclusiveTrips?.userStartLocation?.longitude ??
                            data.endStopLocation?.longitude,
                    icon: AppAssets.iconsCalender.image(
                      height: AppSize.h10,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
