import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/home_module/live_trips_pages/create_trip_pages/models/stock_data_model.dart';
import 'package:transportmatch_provider/presentation/modules/trips_module/models/accepted_trip_model.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/widgets/location_widget.dart';
import 'package:transportmatch_provider/widgets/title_info.dart';

///Upcoming Card Widgets
class UpcomingCardWidgets extends StatelessWidget {
  /// Constructor
  const UpcomingCardWidgets({
    required this.tripModel,
    required this.onDetailsPressed,
    super.key,
  });

  final AcceptedTripModelData tripModel;

  ///For driverName
  final VoidCallback onDetailsPressed;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r4),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: AppSize.h16,
          children: [
            LocationWidget(
              title1: tripModel.exclusiveTrips?.userStartLocation?.street ??
                  tripModel.startStopLocation?.fullAddress ??
                  '',
              startLatitude:
                  tripModel.exclusiveTrips?.userStartLocation?.latitude ??
                      tripModel.startStopLocation?.latitude ??
                      '',
              startLongitude:
                  tripModel.exclusiveTrips?.userStartLocation?.longitude ??
                      tripModel.startStopLocation?.longitude ??
                      '',
              endLatitude:
                  tripModel.exclusiveTrips?.userEndLocation?.latitude ??
                      tripModel.endStopLocation?.latitude ??
                      '',
              endLongitude:
                  tripModel.exclusiveTrips?.userEndLocation?.longitude ??
                      tripModel.endStopLocation?.longitude ??
                      '',
              date1: tripModel.tripStartDate?.monthDate ?? '',
              title2: tripModel.exclusiveTrips?.userEndLocation?.street ??
                  tripModel.endStopLocation?.fullAddress ??
                  '',
              date2: tripModel.tripEndDate?.monthDate ?? '',
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Flexible(
            //       child: LocationInfo(
            //         title:
            //             tripModel.exclusiveTrips?.userStartLocation?.street ??
            //                 tripModel.startStopLocation?.name ??
            //                 '',
            //         date: tripModel.tripStartDate?.monthDate ?? '',
            //         icon: AppAssets.iconsCalender.image(
            //           height: AppSize.h10,
            //         ),
            //       ),
            //     ),
            //     Row(
            //       children: [
            //         AppAssets.iconsLocationOrigin.image(
            //           height: AppSize.h14,
            //         ),
            //         SizedBox(
            //           width: AppSize.w70,
            //           child: const DashedDivider(
            //               /* dashWidth: ApZpSize.w4,
            //                 indent: AppSize.w2,
            //                 endIndent: AppSize.w2,*/
            //               ),
            //         ),
            //         AppAssets.iconsLocation.image(
            //           height: AppSize.h14,
            //         ),
            //       ],
            //     ),
            //     Flexible(
            //       child: LocationInfo(
            //         title: tripModel.exclusiveTrips?.userEndLocation?.street ??
            //             tripModel.endStopLocation?.name ??
            //             '',
            //         date: tripModel.tripEndDate?.monthDate ?? '',
            //         icon: AppAssets.iconsCalender.image(
            //           height: AppSize.h10,
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
            if (tripModel.intermediatePickUpPoint?.isNotEmpty ?? false)
              TitleInfoWidget(
                title: context.l10n.stopLocations,
                subTitle: tripModel.intermediatePickUpPoint
                        ?.map((x) => x.stopLocation?.name)
                        .join(', ') ??
                    '',
                subTitleFontSize: AppSize.sp16,
              ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                TitleInfoWidget(
                  title: context.l10n.driver,
                  subTitle: tripModel.driver?.firstName ?? '',
                  subTitleFontSize: AppSize.sp16,
                ),
                TitleInfoWidget(
                  title: context.l10n.totalVehicle,
                  subTitle: tripModel.totalBookedSlots?.toString() ?? '',
                  subTitleFontSize: AppSize.sp16,
                ),
                GestureDetector(
                  onTap: onDetailsPressed,
                  child: Text(
                    context.l10n.details,
                    style: context.textTheme.titleLarge?.copyWith(
                      color: AppColors.ff0087C7,
                      fontWeight: FontWeight.w600,
                      fontSize: AppSize.sp16,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
