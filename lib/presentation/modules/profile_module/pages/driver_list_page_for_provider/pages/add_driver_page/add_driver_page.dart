// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transportmatch_provider/extensions/ext_build_context.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/profile_module/pages/driver_list_page_for_provider/pages/add_driver_page/provider/add_driver_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_common_functions.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_button.dart';
import 'package:transportmatch_provider/widgets/app_loader.dart';
import 'package:transportmatch_provider/widgets/app_padding.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';
import 'package:transportmatch_provider/widgets/custom_app_bar.dart';

class AddDriverPage extends StatelessWidget {
  const AddDriverPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ChangeNotifierProvider<AddDriverProvider>(
      create: (context) => AddDriverProvider(),
      child: Consumer<AddDriverProvider>(
        builder: (context, addDriverProvider, _) {
          return Form(
            key: addDriverProvider.formKeyAddDriver,
            child: Scaffold(
              backgroundColor: AppColors.ffF8F9FA,
              appBar: CustomAppBar(
                title: l10n.addDriver,
              ),
              body: ValueListenableBuilder(
                valueListenable: addDriverProvider.isShowLoader,
                builder: (context, loading, child) {
                  return AppLoader(
                    isShowLoader: loading,
                    child: child!,
                  );
                },
                child: GestureDetector(
                  onTap: AppCommonFunctions.closeKeyboard,
                  child: AppPadding.symmetric(
                    horizontal: AppSize.appPadding,
                    child: SingleChildScrollView(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppSize.w24,
                          vertical: AppSize.h16,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(AppSize.r6),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: AppSize.h20,
                          children: [
                            Text(
                              l10n.driverInfo,
                              style: context.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w700,
                                fontSize: AppSize.sp20,
                              ),
                            ),

                            // //* UserID
                            // Text(
                            //   '  ${l10n.userID}',
                            //   style: context.textTheme.bodyMedium?.copyWith(
                            //     fontWeight: FontWeight.w600,
                            //     fontSize: AppSize.sp14,
                            //     color: AppColors.black,
                            //   ),
                            // ),
                            // AppPadding(
                            //   top: AppSize.h4,
                            //   bottom: AppSize.h20,
                            //   child: AppTextFormField(
                            //     hintText: l10n.enterIDYouWantToAssign,
                            //     fillColor: AppColors.ffF8F9FA,
                            //     controller: addDriverProvider.userIdController,
                            //     validator: (p0) => userIdValidator().call(p0),
                            //   ),
                            // ),

                            //* Name

                            AppTextFormField(
                              title: l10n.name,
                              hintText: l10n.enterDriversName,
                              textAction:TextInputAction.next,
                              fillColor: AppColors.ffF8F9FA,
                              controller: addDriverProvider.nameController,
                              validator: (p0) => nameValidator().call(p0),
                            ),

                            //* Email

                            AppTextFormField(
                              title: l10n.email,
                              hintText: l10n.enterDriversEmail,
                              textAction:TextInputAction.next,
                              fillColor: AppColors.ffF8F9FA,
                              keyboardType: TextInputType.emailAddress,
                              controller: addDriverProvider.emailController,
                              validator: (p0) => emailValidator().call(p0),
                            ),

                            //* Password

                            ValueListenableBuilder(
                              valueListenable: addDriverProvider.isPasswordShow,
                              builder: (context, isPasswordShow, _) {
                                return AppTextFormField(
                                  title: l10n.password,
                                  hintText: l10n.setPasswordForTheDriver,
                                  fillColor: AppColors.ffF8F9FA,
                                  obscureText:
                                      !addDriverProvider.isPasswordShow.value,
                                  controller:
                                      addDriverProvider.passwordController,
                                  validator: (p0) =>
                                      passwordValidator().call(p0),
                                  suffixIcon: GestureDetector(
                                    onTap: () {
                                      addDriverProvider.isPasswordShow.value =
                                          !addDriverProvider
                                              .isPasswordShow.value;
                                    },
                                    child: Icon(
                                      isPasswordShow
                                          ? Icons.visibility_outlined
                                          : Icons.visibility_off_outlined,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              bottomNavigationBar: AppPadding(
                left: AppSize.appPadding,
                right: AppSize.appPadding,
                bottom: AppSize.h30,
                child: AppButton(
                  text: l10n.addDriver,
                  onPressed: () {
                    if (addDriverProvider.formKeyAddDriver.currentState!
                        .validate()) {
                      addDriverProvider.addDriverApiCall(context: context);
                    }
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
