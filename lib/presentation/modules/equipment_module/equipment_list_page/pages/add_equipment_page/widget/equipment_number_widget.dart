import 'package:flutter/material.dart';
import 'package:transportmatch_provider/extensions/ext_datetime.dart';
import 'package:transportmatch_provider/l10n/l10n.dart';
import 'package:transportmatch_provider/presentation/modules/equipment_module/equipment_list_page/pages/add_equipment_page/provider/add_and_update_equipment_provider.dart';
import 'package:transportmatch_provider/utils/app_colors.dart';
import 'package:transportmatch_provider/utils/app_size.dart';
import 'package:transportmatch_provider/utils/gen/assets.gen.dart';
import 'package:transportmatch_provider/utils/validators/global_text_validator.dart';
import 'package:transportmatch_provider/widgets/app_image.dart';
import 'package:transportmatch_provider/widgets/app_textfield.dart';

class EquipmentNumberWidget extends StatelessWidget {
  const EquipmentNumberWidget({required this.addEquipmentProvider, super.key});

  final AddAndUpdateEquipmentProvider addEquipmentProvider;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.w24,
        vertical: AppSize.h16,
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r6),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: AppSize.h20,
        children: [
          //* Plates
          AppTextFormField(
            title: l10n.plates,
            hintText: l10n.enterPlateNumber,
            textAction:TextInputAction.next,
            fillColor: AppColors.ffF8F9FA,
            controller: addEquipmentProvider.plateNumberController,
            validator: (p0) => equipmentPlateNumberValidator().call(p0),
          ),

          //* Economic Number
          AppTextFormField(
            title: l10n.economicNumber,
            hintText: l10n.enterEconomicNumber,
            textAction:TextInputAction.next,
            fillColor: AppColors.ffF8F9FA,
            controller: addEquipmentProvider.economicNumberController,
            validator: (p0) => equipmentEconomicNumberValidator().call(p0),
          ),

          //* Validity
          AppTextFormField(
            title: l10n.validity,
            hintText: l10n.enterValidityDate,
            readOnly: true,
            fillColor: AppColors.ffF8F9FA,
            onTap: () {
              showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime.now(),
                lastDate: DateTime(DateTime.now().year + 200),
              ).then((pickedDate) {
                if (pickedDate != null) {
                  addEquipmentProvider
                    ..validityDate.value = pickedDate
                    ..notify();
                }
              });
            },
            suffixIcon: AppImage.asset(AppAssets.iconsCalender.path),
            controller: TextEditingController(
              text: addEquipmentProvider.validityDate.value?.passDateFormate,
            ),
            validator: (p0) => equipmentValidityValidator().call(p0),
          ),
        ],
      ),
    );
  }
}
